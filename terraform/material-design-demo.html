<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Material Design | HypatIa</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Material+Icons&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #1976d2;
            --primary-dark: #1565c0;
            --primary-light: #42a5f5;
            --secondary: #388e3c;
            --secondary-dark: #2e7d32;
            --accent: #ff5722;
            --accent-light: #ff8a65;
            --surface: #ffffff;
            --background: #fafafa;
            --error: #d32f2f;
            --on-primary: #ffffff;
            --on-secondary: #ffffff;
            --on-surface: #212121;
            --on-background: #212121;
            --text-primary: rgba(0, 0, 0, 0.87);
            --text-secondary: rgba(0, 0, 0, 0.6);
            --text-disabled: rgba(0, 0, 0, 0.38);
            --divider: rgba(0, 0, 0, 0.12);
            --elevation-1: 0 2px 1px -1px rgba(0,0,0,.2), 0 1px 1px 0 rgba(0,0,0,.14), 0 1px 3px 0 rgba(0,0,0,.12);
            --elevation-2: 0 3px 1px -2px rgba(0,0,0,.2), 0 2px 2px 0 rgba(0,0,0,.14), 0 1px 5px 0 rgba(0,0,0,.12);
            --elevation-4: 0 2px 4px -1px rgba(0,0,0,.2), 0 4px 5px 0 rgba(0,0,0,.14), 0 1px 10px 0 rgba(0,0,0,.12);
            --elevation-8: 0 5px 5px -3px rgba(0,0,0,.2), 0 8px 10px 1px rgba(0,0,0,.14), 0 3px 14px 2px rgba(0,0,0,.12);
        }

        body {
            font-family: 'Roboto', sans-serif;
            background: var(--background);
            color: var(--on-background);
            line-height: 1.5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }

        /* Header */
        .header {
            background: var(--primary);
            color: var(--on-primary);
            padding: 64px 0;
            margin: -24px -24px 32px -24px;
            text-align: center;
            box-shadow: var(--elevation-4);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        .header h1 {
            font-size: 3.75rem;
            font-weight: 300;
            margin-bottom: 16px;
            letter-spacing: -0.01562em;
        }

        .header p {
            font-size: 1.25rem;
            opacity: 0.87;
            font-weight: 400;
        }

        /* Navigation */
        .nav-container {
            background: var(--surface);
            border-radius: 4px;
            box-shadow: var(--elevation-2);
            margin-bottom: 32px;
            overflow: hidden;
        }

        .nav-material {
            padding: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            min-height: 64px;
        }

        .logo {
            font-size: 1.25rem;
            font-weight: 500;
            color: var(--primary);
            text-decoration: none;
            padding: 0 24px;
        }

        .nav-links {
            display: flex;
            list-style: none;
            margin: 0;
        }

        .nav-links a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            padding: 20px 24px;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            position: relative;
            text-transform: uppercase;
            font-size: 0.875rem;
            letter-spacing: 0.0892857143em;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary);
            transform: scaleX(0);
            transition: transform 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: var(--primary);
            background: rgba(25, 118, 210, 0.04);
        }

        .nav-links a:hover::before,
        .nav-links a.active::before {
            transform: scaleX(1);
        }

        /* Cards */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .card {
            background: var(--surface);
            border-radius: 4px;
            box-shadow: var(--elevation-1);
            transition: box-shadow 0.28s cubic-bezier(0.4, 0.0, 0.2, 1);
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--elevation-8);
        }

        .card-content {
            padding: 24px;
        }

        .card h3 {
            font-size: 1.5rem;
            font-weight: 400;
            color: var(--text-primary);
            margin-bottom: 16px;
            line-height: 1.334;
        }

        .card p {
            color: var(--text-secondary);
            margin-bottom: 24px;
            line-height: 1.5;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0 16px;
            height: 36px;
            border-radius: 4px;
            font-weight: 500;
            text-decoration: none;
            text-transform: uppercase;
            letter-spacing: 0.0892857143em;
            font-size: 0.875rem;
            transition: all 0.28s cubic-bezier(0.4, 0.0, 0.2, 1);
            border: none;
            cursor: pointer;
            font-family: 'Roboto', sans-serif;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: currentColor;
            opacity: 0;
            transition: opacity 0.28s cubic-bezier(0.4, 0.0, 0.2, 1);
        }

        .btn:hover::before {
            opacity: 0.04;
        }

        .btn:focus::before {
            opacity: 0.12;
        }

        .btn-contained {
            background: var(--primary);
            color: var(--on-primary);
            box-shadow: var(--elevation-2);
        }

        .btn-contained:hover {
            box-shadow: var(--elevation-4);
        }

        .btn-outlined {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--divider);
        }

        .btn-text {
            background: transparent;
            color: var(--primary);
        }

        /* Form Section */
        .form-section {
            background: var(--surface);
            border-radius: 4px;
            box-shadow: var(--elevation-1);
            overflow: hidden;
        }

        .form-header {
            background: var(--primary);
            color: var(--on-primary);
            padding: 24px;
        }

        .form-header h3 {
            font-size: 1.5rem;
            font-weight: 400;
            margin-bottom: 8px;
        }

        .form-header p {
            opacity: 0.87;
            font-size: 1rem;
        }

        .form-content {
            padding: 24px;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .input-group {
            position: relative;
        }

        .form-control {
            width: 100%;
            padding: 16px 12px 8px 12px;
            border: none;
            border-bottom: 1px solid var(--divider);
            background: transparent;
            font-size: 16px;
            font-family: 'Roboto', sans-serif;
            transition: border-color 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
        }

        .form-control:focus {
            outline: none;
            border-bottom-color: var(--primary);
        }

        .form-label {
            position: absolute;
            top: 16px;
            left: 12px;
            color: var(--text-secondary);
            font-size: 16px;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            pointer-events: none;
        }

        .form-control:focus + .form-label,
        .form-control:not(:placeholder-shown) + .form-label {
            top: 4px;
            font-size: 12px;
            color: var(--primary);
        }

        /* Info Tabs */
        .info-tabs {
            display: flex;
            border-bottom: 1px solid var(--divider);
            margin-bottom: 24px;
        }

        .tab-btn {
            padding: 12px 24px;
            background: transparent;
            border: none;
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            text-transform: uppercase;
            letter-spacing: 0.0892857143em;
            position: relative;
            font-family: 'Roboto', sans-serif;
        }

        .tab-btn::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary);
            transform: scaleX(0);
            transition: transform 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
        }

        .tab-btn:hover,
        .tab-btn.active {
            color: var(--primary);
        }

        .tab-btn.active::before {
            transform: scaleX(1);
        }

        .info-content {
            background: rgba(25, 118, 210, 0.04);
            border-left: 4px solid var(--primary);
            padding: 16px;
            margin-bottom: 24px;
            display: none;
            border-radius: 0 4px 4px 0;
        }

        .info-content.active {
            display: block;
        }

        .info-content p {
            color: var(--text-primary);
            margin: 0;
            line-height: 1.5;
        }

        /* Chip */
        .chip {
            display: inline-flex;
            align-items: center;
            padding: 0 12px;
            height: 32px;
            background: var(--secondary);
            color: var(--on-secondary);
            border-radius: 16px;
            font-size: 0.875rem;
            font-weight: 400;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.125rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .card-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 16px;
            }
            
            .header {
                margin: -16px -16px 24px -16px;
                padding: 48px 0;
            }
            
            .form-content {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <h1>Material Design</h1>
                <p>Google's design system for enterprise cloud tools</p>
            </div>
        </div>

        <!-- Navigation -->
        <div class="nav-container">
            <nav class="nav-material">
                <a href="#" class="logo">HypatIa <span class="chip">Cloud</span></a>
                <ul class="nav-links">
                    <li><a href="#" class="active">Inventory</a></li>
                    <li><a href="#">Compliance</a></li>
                    <li><a href="#">Assessment</a></li>
                    <li><a href="#">Migration</a></li>
                    <li><a href="#">Glide</a></li>
                </ul>
            </nav>
        </div>

        <!-- Cards -->
        <div class="card-grid">
            <div class="card">
                <div class="card-content">
                    <h3>Cloud Inventory</h3>
                    <p>Automated discovery and cataloging of cloud resources across multiple platforms including GCP, AWS, Azure, and Oracle Cloud with detailed reporting.</p>
                    <button class="btn btn-contained">Get Started</button>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content">
                    <h3>Compliance Monitoring</h3>
                    <p>Continuous compliance monitoring and automated reporting for security standards, regulatory requirements, and organizational policies.</p>
                    <button class="btn btn-outlined">Learn More</button>
                </div>
            </div>
            
            <div class="card">
                <div class="card-content">
                    <h3>Migration Planning</h3>
                    <p>Intelligent cloud migration strategies with cost analysis, risk assessment, and automated workload transformation capabilities.</p>
                    <button class="btn btn-contained">Start Planning</button>
                </div>
            </div>
        </div>

        <!-- GCP Inventory Form -->
        <div class="form-section">
            <div class="form-header">
                <h3>GCP Inventory</h3>
                <p>Generate comprehensive inventory workbook for your Google Cloud organization</p>
            </div>
            
            <div class="form-content">
                <!-- Info Tabs -->
                <div class="info-tabs">
                    <button class="tab-btn active" onclick="showTab('requirements')">Requirements</button>
                    <button class="tab-btn" onclick="showTab('workflow')">Workflow</button>
                    <button class="tab-btn" onclick="showTab('output')">Output</button>
                </div>
                
                <!-- Info Content -->
                <div id="requirements" class="info-content active">
                    <p><strong>Access Requirements:</strong><br>
                    • Browser and Viewer access at Organization Level for Cloud Run Service Account<br>
                    • Viewer access at Project level when projects have been selected<br>
                    • Valid GCP credentials with appropriate permissions</p>
                </div>
                
                <div id="workflow" class="info-content">
                    <p><strong>Process Workflow:</strong><br>
                    1. Configure inventory scope using Organization ID, Folder IDs, or Project IDs<br>
                    2. Specify regions for Redis and Memcache services (optional)<br>
                    3. Automated resource discovery and data collection<br>
                    4. Excel workbook generation with comprehensive reporting</p>
                </div>
                
                <div id="output" class="info-content">
                    <p><strong>Generated Output:</strong><br>
                    • Detailed Excel workbook with multi-sheet resource inventory<br>
                    • Service categorization and resource utilization metrics<br>
                    • Cost analysis and optimization recommendations<br>
                    • Security and compliance status overview</p>
                </div>
                
                <!-- Form -->
                <form>
                    <div class="form-group">
                        <div class="input-group">
                            <input type="text" id="orgid" class="form-control" placeholder=" ">
                            <label for="orgid" class="form-label">Organization ID</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="input-group">
                            <input type="text" id="folderids" class="form-control" placeholder=" ">
                            <label for="folderids" class="form-label">Folder IDs (comma-separated)</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="input-group">
                            <input type="text" id="projids" class="form-control" placeholder=" ">
                            <label for="projids" class="form-label">Project IDs (comma-separated)</label>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <div class="input-group">
                            <input type="text" id="regions" class="form-control" placeholder=" ">
                            <label for="regions" class="form-label">Redis/Memcache Regions</label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-contained">Generate Inventory</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            document.querySelectorAll('.info-content').forEach(content => {
                content.classList.remove('active');
            });
            
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
