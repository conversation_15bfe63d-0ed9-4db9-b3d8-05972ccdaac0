<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Minimalist Design | HypatIa</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-blue: #2563eb;
            --secondary-blue: #1e40af;
            --accent-teal: #0891b2;
            --accent-green: #059669;
            --accent-orange: #ea580c;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --white: #ffffff;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--gray-50);
            color: var(--gray-900);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 60px;
            animation: fadeInUp 0.8s ease-out;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 16px;
            letter-spacing: -0.025em;
        }

        .header p {
            font-size: 1.25rem;
            color: var(--gray-600);
            font-weight: 400;
        }

        /* Navigation */
        .nav-container {
            background: var(--white);
            border-radius: 16px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            margin-bottom: 40px;
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        .nav-minimalist {
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-blue);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 32px;
            list-style: none;
        }

        .nav-links a {
            color: var(--gray-600);
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: var(--primary-blue);
            background: var(--gray-100);
        }

        /* Cards */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }

        .card {
            background: var(--white);
            border-radius: 12px;
            padding: 24px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            transition: all 0.3s ease;
            animation: fadeInUp 0.8s ease-out calc(0.4s + var(--delay, 0s)) both;
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-4px);
        }

        .card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 12px;
        }

        .card p {
            color: var(--gray-600);
            margin-bottom: 20px;
            line-height: 1.6;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary {
            background: var(--primary-blue);
            color: var(--white);
        }

        .btn-primary:hover {
            background: var(--secondary-blue);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-secondary {
            background: var(--gray-100);
            color: var(--gray-700);
            border: 1px solid var(--gray-300);
        }

        .btn-secondary:hover {
            background: var(--gray-200);
        }

        /* Form Section */
        .form-section {
            background: var(--white);
            border-radius: 16px;
            padding: 32px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-200);
            animation: fadeInUp 0.8s ease-out 0.6s both;
        }

        .form-section h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: 24px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 6px;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.2s ease;
            background: var(--white);
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
        }

        .form-control::placeholder {
            color: var(--gray-400);
        }

        /* Info Tabs */
        .info-tabs {
            display: flex;
            gap: 12px;
            margin-bottom: 24px;
            flex-wrap: wrap;
        }

        .tab-btn {
            padding: 8px 16px;
            background: var(--gray-100);
            border: 1px solid var(--gray-200);
            border-radius: 6px;
            color: var(--gray-600);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .tab-btn:hover,
        .tab-btn.active {
            background: var(--primary-blue);
            color: var(--white);
            border-color: var(--primary-blue);
        }

        .info-content {
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            display: none;
        }

        .info-content.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        .info-content p {
            color: var(--gray-600);
            margin: 0;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .card-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 20px 15px;
            }
            
            .form-section {
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Modern Minimalist</h1>
            <p>Clean, professional cloud automation interface</p>
        </div>

        <!-- Navigation -->
        <div class="nav-container">
            <nav class="nav-minimalist">
                <a href="#" class="logo">HypatIa</a>
                <ul class="nav-links">
                    <li><a href="#" class="active">Inventory</a></li>
                    <li><a href="#">Compliance</a></li>
                    <li><a href="#">Assessment</a></li>
                    <li><a href="#">Migration</a></li>
                    <li><a href="#">Glide</a></li>
                </ul>
            </nav>
        </div>

        <!-- Cards -->
        <div class="card-grid">
            <div class="card" style="--delay: 0s">
                <h3>Cloud Inventory</h3>
                <p>Generate comprehensive inventory reports across GCP, AWS, Azure, and Oracle Cloud platforms with automated discovery.</p>
                <a href="#" class="btn btn-primary">Get Started</a>
            </div>
            
            <div class="card" style="--delay: 0.1s">
                <h3>Compliance Monitoring</h3>
                <p>Automated security compliance checking and reporting for multi-cloud environments with detailed analytics.</p>
                <a href="#" class="btn btn-secondary">Learn More</a>
            </div>
            
            <div class="card" style="--delay: 0.2s">
                <h3>Migration Planning</h3>
                <p>Intelligent cloud migration planning and execution with cost optimization and risk assessment tools.</p>
                <a href="#" class="btn btn-primary">Explore</a>
            </div>
        </div>

        <!-- GCP Inventory Form -->
        <div class="form-section">
            <h3>GCP Inventory Generation</h3>
            <p style="color: var(--gray-600); margin-bottom: 24px;">Generate comprehensive GCP inventory workbook for your organization</p>
            
            <!-- Info Tabs -->
            <div class="info-tabs">
                <button class="tab-btn active" onclick="showTab('requirements')">Requirements</button>
                <button class="tab-btn" onclick="showTab('steps')">Steps</button>
                <button class="tab-btn" onclick="showTab('output')">Output</button>
            </div>
            
            <!-- Info Content -->
            <div id="requirements" class="info-content active">
                <p><strong>Requirements:</strong><br>
                • Browser and Viewer access at Organization Level for Cloud Run Service Account<br>
                • Viewer access at Project level when projects have been selected</p>
            </div>
            
            <div id="steps" class="info-content">
                <p><strong>Steps:</strong><br>
                1. Fill in at least one field based on your requirements<br>
                2. Enter GCP Organization ID for whole organization inventory<br>
                3. Enter Folder IDs (comma-separated) for specific folders<br>
                4. Enter Project IDs (comma-separated) for specific projects</p>
            </div>
            
            <div id="output" class="info-content">
                <p><strong>Output:</strong><br>
                Comprehensive Excel workbook with detailed inventory across all GCP services including compute, storage, networking, and security resources.</p>
            </div>
            
            <!-- Form -->
            <form>
                <div class="form-group">
                    <label for="orgid">Organization ID</label>
                    <input type="text" id="orgid" class="form-control" placeholder="Enter Organization ID">
                </div>
                
                <div class="form-group">
                    <label for="folderids">Folder IDs</label>
                    <input type="text" id="folderids" class="form-control" placeholder="folder1,folder2,folder3">
                </div>
                
                <div class="form-group">
                    <label for="projids">Project IDs</label>
                    <input type="text" id="projids" class="form-control" placeholder="project1,project2,project3">
                </div>
                
                <div class="form-group">
                    <label for="regions">Redis/Memcache Regions</label>
                    <input type="text" id="regions" class="form-control" placeholder="us-central1,us-east1">
                </div>
                
                <button type="submit" class="btn btn-primary">Generate Inventory</button>
            </form>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all content
            document.querySelectorAll('.info-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active from all buttons
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected content
            document.getElementById(tabName).classList.add('active');
            
            // Mark button as active
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
