<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liquid Glass Design Demo | HypatIa</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Animated background particles */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(120deg); }
            66% { transform: translateY(30px) rotate(240deg); }
        }

        /* Glass morphism base styles */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .glass-strong {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
        }

        .glass-subtle {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
        }

        .header h1 {
            color: white;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            animation: slideInDown 1s ease-out;
        }

        .header p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.2rem;
            font-weight: 300;
            animation: slideInUp 1s ease-out 0.3s both;
        }

        /* Navigation Demo */
        .nav-demo {
            margin-bottom: 40px;
            animation: slideInLeft 1s ease-out 0.6s both;
        }

        .nav-glass {
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .nav-glass:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .logo {
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: 30px;
            list-style: none;
        }

        .nav-links a {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 10px 20px;
            border-radius: 10px;
        }

        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transform: translateY(-2px);
        }

        /* Card Grid */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .card {
            padding: 30px;
            transition: all 0.4s ease;
            animation: slideInUp 1s ease-out calc(0.9s + var(--delay, 0s)) both;
        }

        .card:hover {
            transform: translateY(-10px) scale(1.02);
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 
                0 20px 60px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .card h3 {
            color: white;
            font-size: 1.5rem;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .card p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 20px;
        }

        /* Buttons */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .btn-glass {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .btn-glass:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        /* Form Demo */
        .form-demo {
            margin-bottom: 40px;
            animation: slideInRight 1s ease-out 1.2s both;
        }

        .form-glass {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            color: white;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 15px 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-control:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.4);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        }

        /* Animations */
        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .card-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="bg-animation"></div>
    
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Liquid Glass Design</h1>
            <p>Experience the future of UI with glassmorphism effects</p>
        </div>

        <!-- Navigation Demo -->
        <div class="nav-demo">
            <nav class="glass nav-glass">
                <a href="#" class="logo">HypatIa</a>
                <ul class="nav-links">
                    <li><a href="#">Inventory</a></li>
                    <li><a href="#">Compliance</a></li>
                    <li><a href="#">Assessment</a></li>
                    <li><a href="#">Migration</a></li>
                    <li><a href="#">Glide</a></li>
                </ul>
            </nav>
        </div>

        <!-- Card Grid -->
        <div class="card-grid">
            <div class="card glass" style="--delay: 0s">
                <h3>Cloud Inventory</h3>
                <p>Generate comprehensive inventory reports across GCP, AWS, Azure, and Oracle Cloud platforms with real-time data collection.</p>
                <a href="#" class="btn btn-primary">Explore</a>
            </div>
            
            <div class="card glass" style="--delay: 0.2s">
                <h3>Compliance Monitoring</h3>
                <p>Automated security compliance checking and reporting for multi-cloud environments with detailed analytics.</p>
                <a href="#" class="btn btn-glass">Learn More</a>
            </div>
            
            <div class="card glass" style="--delay: 0.4s">
                <h3>Migration Tools</h3>
                <p>Streamlined cloud migration planning and execution with intelligent resource mapping and cost optimization.</p>
                <a href="#" class="btn btn-primary">Get Started</a>
            </div>
        </div>

        <!-- Form Demo -->
        <div class="form-demo">
            <div class="glass form-glass">
                <h3 style="color: white; margin-bottom: 30px; font-size: 1.8rem;">Try the Glass Form</h3>
                <form>
                    <div class="form-group">
                        <label for="project">Project ID</label>
                        <input type="text" id="project" class="form-control" placeholder="Enter your project ID">
                    </div>
                    <div class="form-group">
                        <label for="region">Region</label>
                        <input type="text" id="region" class="form-control" placeholder="us-central1">
                    </div>
                    <div class="form-group">
                        <label for="service">Service Type</label>
                        <select class="form-control">
                            <option>GCP Inventory</option>
                            <option>AWS Inventory</option>
                            <option>Azure Inventory</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Generate Report</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Parallax effect for background
            document.addEventListener('mousemove', function(e) {
                const bg = document.querySelector('.bg-animation');
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;
                
                bg.style.transform = `translate(${x * 20}px, ${y * 20}px)`;
            });

            // Card hover effects
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.background = 'rgba(255, 255, 255, 0.2)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.background = 'rgba(255, 255, 255, 0.1)';
                });
            });
        });
    </script>
</body>
</html>
