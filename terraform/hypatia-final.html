<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Hybrid Cloud Deployment and Resource Automation | Searce</title>
    <link rel="icon" sizes="180x180" href="./static/assets/images/hypatia.png" />

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    
    <!-- Custom Liquid Glass CSS -->
    <link rel="stylesheet" href="./static/styles/liquid-glass.css" />
    
    <!-- Original CSS for compatibility -->
    <link rel="stylesheet" href="./static/styles/on.css" />
    <link rel="stylesheet" href="./static/styles/app.min.css" type="text/css" media="all" />
    
    <style>
        @font-face {
            font-family: 'HeilHydra';
            src: url('./static/assets/fonts/hydra.ttf') format('truetype');
        }

        /* Black & White Dashboard Layout */
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: #ffffff;
            position: relative;
            overflow-x: hidden;
        }

        .dashboard-container {
            display: grid;
            grid-template-areas: 
                "sidebar header"
                "sidebar main";
            grid-template-columns: 280px 1fr;
            grid-template-rows: 80px 1fr;
            min-height: 100vh;
        }

        /* Sidebar - Pure Black */
        .sidebar {
            grid-area: sidebar;
            background: #000000;
            border-right: 1px solid #333333;
            padding: 20px;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            margin-bottom: 40px;
            padding: 20px 0;
            border-bottom: 1px solid #333333;
        }

        .sidebar-logo img {
            width: 32px;
            height: 32px;
            margin-right: 12px;
        }

        .sidebar-logo .brand-text {
            font-family: 'HeilHydra', sans-serif;
            font-size: 24px;
            color: #ffffff;
        }

        .sidebar-logo .beta-text {
            font-size: 8px;
            vertical-align: super;
            color: #06b6d4;
        }

        .sidebar-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-nav li {
            margin-bottom: 8px;
        }

        .sidebar-nav a {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: #cccccc;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            transform: translateX(4px);
        }

        .sidebar-nav a.active {
            background: rgba(255, 255, 255, 0.15);
            border-left: 3px solid #3b82f6;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            opacity: 0.7;
        }

        /* Header - Pure White */
        .header {
            grid-area: header;
            background: #ffffff;
            border-bottom: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000000;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #000000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        /* Main Content - Pure White */
        .main-content {
            grid-area: main;
            padding: 20px;
            overflow-y: auto;
            background: #ffffff;
            max-height: calc(100vh - 80px);
        }

        /* Override original styles for compact forms */
        .tab-content {
            padding: 0;
        }

        .nav-container {
            margin-bottom: 20px;
        }

        .nav-tabs {
            border: none;
            justify-content: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .nav-tabs li {
            margin: 5px;
        }

        .nav-tabs a {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 10px 16px;
            color: #666666;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            font-size: 14px;
        }

        .nav-tabs a:hover,
        .nav-tabs a.active {
            background: rgba(255, 255, 255, 0.2);
            color: #000000;
            transform: translateY(-1px);
            text-decoration: none;
            border-color: rgba(0, 0, 0, 0.2);
        }

        /* Compact form styling */
        .form-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            color: #000000;
            margin-bottom: 5px;
            font-weight: 500;
            font-size: 14px;
            display: block;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            color: #000000;
            padding: 10px 12px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.1);
            border-color: #3b82f6;
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.1);
        }

        .form-control::placeholder {
            color: #999999;
        }

        .btn {
            background: linear-gradient(135deg, #000000 0%, #333333 100%);
            border: none;
            border-radius: 8px;
            color: #ffffff;
            padding: 10px 20px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        /* Info tabs styling */
        .info-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .info-tab-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 6px;
            padding: 6px 12px;
            color: #666666;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .info-tab-btn:hover,
        .info-tab-btn.active {
            background: rgba(255, 255, 255, 0.2);
            color: #000000;
        }

        .info-content {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            display: none;
            font-size: 13px;
            line-height: 1.5;
        }

        .info-content.active {
            display: block;
        }

        .info-content p {
            color: #666666;
            margin: 0;
        }

        /* Hidden class for sections */
        .hidden {
            display: none !important;
        }

        /* Chatbot Styles */
        .chatbot-container {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 10000;
        }

        .chatbot-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chatbot-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .chatbot-button span {
            font-size: 24px;
        }

        .chatbot-window {
            position: absolute;
            bottom: 80px;
            right: 0;
            width: 350px;
            height: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            display: none;
            flex-direction: column;
            overflow: hidden;
        }

        .chatbot-window.open {
            display: flex;
            animation: slideInUp 0.3s ease-out;
        }

        .chatbot-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chatbot-header h4 {
            margin: 0;
            color: #000000;
            font-size: 16px;
            font-weight: 600;
        }

        .chatbot-close {
            background: none;
            border: none;
            color: #666666;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .chatbot-close:hover {
            background: rgba(0, 0, 0, 0.1);
            color: #000000;
        }

        .chatbot-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 16px;
            font-size: 14px;
            line-height: 1.4;
        }

        .message.user {
            align-self: flex-end;
            background: rgba(0, 0, 0, 0.8);
            color: #ffffff;
        }

        .message.bot {
            align-self: flex-start;
            background: rgba(255, 255, 255, 0.1);
            color: #000000;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .chatbot-input-container {
            padding: 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 12px;
        }

        .chatbot-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            padding: 12px 16px;
            color: #000000;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .chatbot-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.1);
            border-color: #3b82f6;
        }

        .chatbot-send {
            background: rgba(0, 0, 0, 0.8);
            border: none;
            border-radius: 12px;
            color: #ffffff;
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .chatbot-send:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: translateY(-1px);
        }

        .typing-indicator {
            display: flex;
            gap: 4px;
            padding: 12px 16px;
            align-self: flex-start;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #666666;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.5;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-areas:
                    "header"
                    "main";
                grid-template-columns: 1fr;
                grid-template-rows: 80px 1fr;
            }

            .sidebar {
                display: none;
            }

            .main-content {
                padding: 15px;
            }

            .chatbot-container {
                bottom: 20px;
                right: 20px;
            }

            .chatbot-window {
                width: 300px;
                height: 400px;
            }
        }
    </style>
</head>

<body id="the-body">
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-logo">
                <img src="./static/assets/images/hypatia.png" alt="HypatIa">
                <span class="brand-text">HypatIa<sup class="beta-text">BETA</sup></span>
            </div>
            
            <nav>
                <ul class="sidebar-nav">
                    <li><a href="#" class="nav-link active" onclick="switchCont(1)">
                        <span class="nav-icon">📦</span>
                        Inventory
                    </a></li>
                    <li><a href="#" class="nav-link" onclick="switchCont(2)">
                        <span class="nav-icon">🛡️</span>
                        Compliance
                    </a></li>
                    <li><a href="#" class="nav-link" onclick="switchCont(3)">
                        <span class="nav-icon">📋</span>
                        Assessment
                    </a></li>
                    <li><a href="#" class="nav-link" onclick="switchCont(4)">
                        <span class="nav-icon">🚀</span>
                        Migration
                    </a></li>
                    <li><a href="#" class="nav-link" onclick="switchCont(5)">
                        <span class="nav-icon">⚡</span>
                        Glide
                    </a></li>
                </ul>
            </nav>
        </aside>

        <!-- Header -->
        <header class="header">
            <h1 class="header-title" id="page-title">Cloud Inventory Management</h1>
            <div class="header-actions">
                <div class="user-profile">
                    <div class="user-avatar">U</div>
                    <span style="color: #666666;">Admin User</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div id="astirth" class="tab-content">
                <!-- Inventory Section (div1) -->
                <div id="div1" class="tab-pane fade in active show">
                    <div class="nav-container">
                        <ul class="nav nav-tabs">
                            <li><a class="active" data-toggle="tab" href="#software">
                                <img src="./static/assets/images/azinv1.png" style="width: 20px; height: 20px; margin-right: 8px;" />
                                <span>GCP Inventory</span>
                            </a></li>
                            <li><a data-toggle="tab" href="#aws1">
                                <img src="./static/assets/images/awsinv1.png" style="width: 20px; height: 20px; margin-right: 8px;" />
                                <span>AWS Inventory</span>
                            </a></li>
                            <li><a data-toggle="tab" href="#ari">
                                <img src="./static/assets/images/inv1.png" style="width: 20px; height: 20px; margin-right: 8px;" />
                                <span>Azure Inventory</span>
                            </a></li>
                            <li><a data-toggle="tab" href="#oci_inv">
                                <img src="./static/assets/images/oci_inv1.png" style="width: 20px; height: 20px; margin-right: 8px;" />
                                <span>Oracle Inventory</span>
                            </a></li>
                            <li><a data-toggle="tab" href="#gke_dragon">
                                <img src="./static/assets/images/google-gke.svg" style="width: 20px; height: 20px; margin-right: 8px;" />
                                <span>GKE Inventory</span>
                            </a></li>
                            <li><a data-toggle="tab" href="#eks">
                                <img src="./static/assets/images/eksinv.png" style="width: 20px; height: 20px; margin-right: 8px;" />
                                <span>EKS Inventory</span>
                            </a></li>
                        </ul>
                    </div>

                    <div class="tab-content">
                        <!-- GCP Inventory Tab -->
                        <div id="software" class="tab-pane fade in active show">
                            <div class="form-container">
                                <h4 style="margin-bottom: 15px; color: #000000;">
                                    Generate GCP Inventory Workbook for an organisation
                                    <span style="color: #06b6d4"> by giving org id</span>
                                </h4>

                                <!-- Info Tabs -->
                                <div class="info-tabs">
                                    <button class="info-tab-btn" onclick="openCity(event, 'gcp_req')">Requirements</button>
                                    <button class="info-tab-btn" onclick="openCity(event, 'gcp_steps')">Steps</button>
                                    <button class="info-tab-btn" onclick="openCity(event, 'gcp_out')">Output</button>
                                    <button class="info-tab-btn" onclick="openCity(event, 'gcp_vid')">Video</button>
                                    <button class="info-tab-btn" onclick="openCity(event, 'gcp_wn')">What Next?</button>
                                </div>

                                <!-- Info Content -->
                                <div id="gcp_req" class="info-content">
                                    <p><strong>Requirements:</strong><br>
                                    • Browser and Viewer access at Organization Level for Cloud Run Service Account<br>
                                    • Viewer access at Project level when projects have been selected</p>
                                </div>

                                <div id="gcp_steps" class="info-content">
                                    <p><strong>Steps:</strong><br>
                                    1. Fill in at least one field based on your requirements<br>
                                    2. Enter GCP Organization ID to create an inventory for the whole organization<br>
                                    3. Enter Folder IDs (comma-separated) to create an inventory for specific folders<br>
                                    4. Enter Project IDs (comma-separated) to create an inventory for specific projects<br>
                                    5. To include Redis, provide comma separated regions. Default region is us-central1</p>
                                </div>

                                <div id="gcp_out" class="info-content">
                                    <a href="https://docs.google.com/spreadsheets/d/1_D9pkJiBefJ1MXF9KGN97sz_h1qmYFT-/view"
                                       target="_blank" class="btn" style="margin-bottom: 15px;">Sample Output</a>
                                    <div style="text-align: center;">
                                        <img src="static/assets/images/sample-output-gcp-inv.png" style="max-width: 100%; border-radius: 8px;" />
                                    </div>
                                </div>

                                <div id="gcp_vid" class="info-content">
                                    <iframe src="https://drive.google.com/file/d/1NZ96Cgwku385dW0A8MSAssljRudJH3n3/preview"
                                            width="100%" height="300" allow="autoplay; fullscreen;" style="border-radius: 8px;"></iframe>
                                </div>

                                <div id="gcp_wn" class="info-content">
                                    <p>No further action is required.</p>
                                </div>

                                <!-- Form Section -->
                                <form id="form-gcp" action="inventory" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'gcp','');">
                                    <input type="hidden" name="call" value="gcp">

                                    <div class="form-group">
                                        <label>Organization ID</label>
                                        <input type="text" placeholder="Enter Organization ID" name="orgid" class="form-control">
                                    </div>

                                    <div class="form-group">
                                        <label>Folder IDs</label>
                                        <input type="text" placeholder="folder1,folder2,folder3" name="folderids" class="form-control">
                                    </div>

                                    <div class="form-group">
                                        <label>Project IDs</label>
                                        <input type="text" placeholder="project1,project2,project3" name="projids" class="form-control">
                                    </div>

                                    <div class="form-group">
                                        <label>Redis/Memcache Regions</label>
                                        <input type="text" placeholder="us-central1,us-east1" name="redis-reg" class="form-control">
                                    </div>

                                    <button type="submit" class="btn">Generate</button>
                                </form>

                                <div id="text-block-container-gcp" style="filter:none; margin-top: 15px;"></div>
                            </div>
                        </div>

                <!-- Compliance Section (div2) -->
                <div id="div2" class="hidden">
                    <!-- Content will be loaded here -->
                </div>

                <!-- Assessment Section (div3) -->
                <div id="div3" class="hidden">
                    <!-- Content will be loaded here -->
                </div>

                <!-- Migration Section (div4) -->
                <div id="div4" class="hidden">
                    <!-- Content will be loaded here -->
                </div>

                <!-- Glide Section (div5) -->
                <div id="div5" class="hidden">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </main>
    </div>

    <!-- Chatbot -->
    <div class="chatbot-container">
        <div class="chatbot-window" id="chatbot-window">
            <div class="chatbot-header">
                <h4>🤖 HypatIa Assistant</h4>
                <button class="chatbot-close" onclick="toggleChatbot()">×</button>
            </div>
            <div class="chatbot-messages" id="chatbot-messages">
                <div class="message bot">
                    Hello! I'm your HypatIa AI assistant. I can help you with cloud inventory, compliance, assessments, and migration planning. How can I assist you today?
                </div>
            </div>
            <div class="chatbot-input-container">
                <input type="text" class="chatbot-input" id="chatbot-input" placeholder="Type your message..." onkeypress="handleChatKeyPress(event)">
                <button class="chatbot-send" onclick="sendMessage()" id="chatbot-send">Send</button>
            </div>
        </div>
        <div class="chatbot-button" onclick="toggleChatbot()">
            <span>🤖</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="./static/scripts/main.js"></script>
    
    <script>
        // Navigation functionality
        function switchCont(invoker) {
            // Update navigation active state
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            document.querySelector(`[onclick="switchCont(${invoker})"]`).classList.add('active');
            
            // Update page title
            const titles = {
                1: 'Cloud Inventory Management',
                2: 'Compliance Monitoring', 
                3: 'Cloud Assessment Tools',
                4: 'Migration Planning',
                5: 'Glide Automation'
            };
            document.getElementById('page-title').textContent = titles[invoker];
            
            // Show/hide sections
            for (var i = 1; i <= 5; i++) {
                var div = document.getElementById('div' + i);
                if (i === invoker) {
                    div.classList.remove('hidden');
                    div.classList.add('active', 'show');
                } else {
                    div.classList.add('hidden');
                    div.classList.remove('active', 'show');
                }
            }
            
            // Load section content if not already loaded
            loadSectionContent(invoker);
        }
        
        function loadSectionContent(sectionNum) {
            const section = document.getElementById('div' + sectionNum);
            if (section && section.innerHTML.trim() === '<!-- Content will be loaded here -->') {
                // Load content based on section number
                // This will be implemented in the next part
            }
        }

        // Chatbot functionality
        let chatbotOpen = false;
        let messageHistory = [];

        function toggleChatbot() {
            const chatbotWindow = document.getElementById('chatbot-window');
            chatbotOpen = !chatbotOpen;

            if (chatbotOpen) {
                chatbotWindow.classList.add('open');
                document.getElementById('chatbot-input').focus();
            } else {
                chatbotWindow.classList.remove('open');
            }
        }

        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        async function sendMessage() {
            const input = document.getElementById('chatbot-input');
            const message = input.value.trim();

            if (!message) return;

            // Add user message
            addMessage(message, 'user');
            input.value = '';

            // Show typing indicator
            showTypingIndicator();

            // Disable send button
            const sendButton = document.getElementById('chatbot-send');
            sendButton.disabled = true;

            try {
                // Call Vertex AI API
                const response = await callVertexAI(message);

                // Remove typing indicator
                removeTypingIndicator();

                // Add bot response
                addMessage(response, 'bot');
            } catch (error) {
                console.error('Error calling Vertex AI:', error);
                removeTypingIndicator();
                addMessage('I apologize, but I\'m having trouble connecting to my AI service right now. Please try again later.', 'bot');
            } finally {
                // Re-enable send button
                sendButton.disabled = false;
            }
        }

        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chatbot-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.textContent = text;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // Store in history
            messageHistory.push({ text, sender, timestamp: new Date() });
        }

        function showTypingIndicator() {
            const messagesContainer = document.getElementById('chatbot-messages');
            const typingDiv = document.createElement('div');
            typingDiv.className = 'typing-indicator';
            typingDiv.id = 'typing-indicator';
            typingDiv.innerHTML = '<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>';

            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function removeTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        async function callVertexAI(message) {
            // This is a placeholder for Vertex AI integration
            // In a real implementation, you would call the Vertex AI API

            // Get current active section
            const activeSection = document.querySelector('.nav-link.active');
            const sectionText = activeSection ? activeSection.textContent.trim() : 'Inventory';

            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

            // Generate contextual responses
            const responses = {
                'Inventory': [
                    "I can help you generate cloud inventory reports for GCP, AWS, Azure, Oracle Cloud, GKE, and EKS. Which platform would you like to start with?",
                    "For GCP inventory, you'll need Organization ID, Folder IDs, or Project IDs. Make sure you have the required permissions set up.",
                    "AWS inventory requires an ARN with ReadOnlyAccess policy. I can guide you through the CloudFormation setup if needed.",
                    "Azure inventory needs a Service Principal with Reader access. Would you like help setting up the credentials?"
                ],
                'Compliance': [
                    "I can assist with compliance monitoring across GCP, AWS, and Azure. Which compliance framework are you interested in - CIS, NIST, ISO 27001, or SOC 2?",
                    "For GCP compliance, you'll need Security Reviewer access and enabled APIs. I can help you understand the requirements.",
                    "AWS compliance scanning supports multiple frameworks including AWS Foundational Security Standard and PCI DSS.",
                    "Azure compliance assessment covers Azure Security Benchmark and various industry standards."
                ],
                'Assessment': [
                    "I can help with cloud assessments including migration readiness, VMware analysis, and cost assessments. What type of assessment are you planning?",
                    "For migration assessment, I'll need information about your current infrastructure and target cloud platform.",
                    "VMware assessment requires vCenter access and helps plan your cloud migration strategy.",
                    "Cost assessment can provide TCO analysis and ROI calculations for your cloud migration."
                ],
                'Migration': [
                    "I can assist with migration planning using the 6 R's strategy: Rehost, Replatform, Refactor, Repurchase, Retain, or Retire. Which approach fits your needs?",
                    "Migration planning involves assessing your current environment and creating detailed build sheets for the target platform.",
                    "I can help you create migration timelines and track progress throughout your cloud journey.",
                    "Build sheets provide detailed specifications for recreating your infrastructure in the cloud."
                ],
                'Glide': [
                    "Glide automation provides advanced cloud operations including automated provisioning, optimization, and monitoring.",
                    "I can help you set up automated provisioning workflows to streamline your infrastructure deployment.",
                    "Continuous optimization features help reduce costs and improve performance automatically.",
                    "Smart monitoring with predictive analytics can help prevent issues before they occur."
                ]
            };

            // Get responses for current section or general response
            const sectionResponses = responses[sectionText] || [
                "I'm here to help with all aspects of cloud management including inventory, compliance, assessments, migration, and automation.",
                "You can ask me about any of the HypatIa features - I have detailed knowledge about cloud inventory generation, compliance monitoring, migration planning, and more.",
                "Feel free to ask specific questions about cloud platforms, requirements, or step-by-step guidance for any process."
            ];

            // Simple keyword matching for more relevant responses
            const lowerMessage = message.toLowerCase();
            if (lowerMessage.includes('gcp') || lowerMessage.includes('google')) {
                return "For Google Cloud Platform, I can help with inventory generation, compliance scanning, and migration planning. You'll typically need Organization ID and appropriate IAM permissions.";
            } else if (lowerMessage.includes('aws') || lowerMessage.includes('amazon')) {
                return "For AWS, I can assist with inventory reports, compliance assessments, and EKS analysis. You'll need an ARN with ReadOnlyAccess permissions.";
            } else if (lowerMessage.includes('azure') || lowerMessage.includes('microsoft')) {
                return "For Microsoft Azure, I can help with inventory generation and compliance monitoring. You'll need a Service Principal with Reader access.";
            } else if (lowerMessage.includes('help') || lowerMessage.includes('how')) {
                return "I'm here to guide you through any HypatIa process. You can ask about specific requirements, step-by-step instructions, or best practices for cloud management.";
            }

            return sectionResponses[Math.floor(Math.random() * sectionResponses.length)];
        }

        // Info tab functionality
        function openCity(evt, cityName) {
            // Check if the clicked tab is already active
            if (document.getElementById(cityName).classList.contains('active')) {
                document.getElementById(cityName).classList.remove('active');
                evt.currentTarget.classList.remove('active');
                return;
            }

            // Get the parent container to scope the search
            const parentContainer = evt.currentTarget.closest('.form-container');

            // Hide all info content in the same parent
            const tabContents = parentContainer.getElementsByClassName("info-content");
            for (let i = 0; i < tabContents.length; i++) {
                tabContents[i].classList.remove('active');
            }

            // Remove active class from all buttons in the same parent
            const tabButtons = parentContainer.getElementsByClassName("info-tab-btn");
            for (let i = 0; i < tabButtons.length; i++) {
                tabButtons[i].classList.remove('active');
            }

            // Show the selected content and mark button as active
            const targetContent = document.getElementById(cityName);
            if (targetContent) {
                targetContent.classList.add('active');
                evt.currentTarget.classList.add('active');
            }
        }
    </script>
</body>
</html>
