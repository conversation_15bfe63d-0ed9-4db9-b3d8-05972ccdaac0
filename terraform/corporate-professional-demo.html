<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Corporate Professional Design | HypatIa</title>
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-navy: #1e3a8a;
            --secondary-navy: #1e40af;
            --accent-blue: #3b82f6;
            --accent-teal: #0d9488;
            --accent-green: #16a34a;
            --accent-orange: #f59e0b;
            --neutral-50: #fafafa;
            --neutral-100: #f5f5f5;
            --neutral-200: #e5e5e5;
            --neutral-300: #d4d4d4;
            --neutral-400: #a3a3a3;
            --neutral-500: #737373;
            --neutral-600: #525252;
            --neutral-700: #404040;
            --neutral-800: #262626;
            --neutral-900: #171717;
            --white: #ffffff;
        }

        body {
            font-family: 'Source Sans Pro', sans-serif;
            background: var(--neutral-50);
            color: var(--neutral-800);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-navy) 0%, var(--secondary-navy) 100%);
            color: var(--white);
            padding: 60px 0;
            margin: -40px -20px 40px -20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 16px;
            letter-spacing: -0.02em;
        }

        .header p {
            font-size: 1.25rem;
            opacity: 0.9;
            font-weight: 300;
        }

        /* Navigation */
        .nav-container {
            background: var(--white);
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            margin-bottom: 40px;
            border-left: 4px solid var(--accent-blue);
        }

        .nav-corporate {
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--primary-navy);
            text-decoration: none;
            font-family: 'Roboto', sans-serif;
        }

        .nav-links {
            display: flex;
            gap: 32px;
            list-style: none;
        }

        .nav-links a {
            color: var(--neutral-600);
            text-decoration: none;
            font-weight: 600;
            padding: 12px 20px;
            border-radius: 6px;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 3px;
            background: var(--accent-blue);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: var(--primary-navy);
            background: var(--neutral-100);
        }

        .nav-links a:hover::before,
        .nav-links a.active::before {
            width: 80%;
        }

        /* Cards */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .card {
            background: var(--white);
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 4px rgb(0 0 0 / 0.1);
            border: 1px solid var(--neutral-200);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-teal));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 8px 25px rgb(0 0 0 / 0.15);
            transform: translateY(-5px);
        }

        .card:hover::before {
            transform: scaleX(1);
        }

        .card h3 {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-navy);
            margin-bottom: 16px;
            font-family: 'Roboto', sans-serif;
        }

        .card p {
            color: var(--neutral-600);
            margin-bottom: 24px;
            line-height: 1.7;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-family: 'Source Sans Pro', sans-serif;
        }

        .btn-primary {
            background: var(--primary-navy);
            color: var(--white);
        }

        .btn-primary:hover {
            background: var(--secondary-navy);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgb(30 58 138 / 0.4);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-navy);
            border: 2px solid var(--primary-navy);
        }

        .btn-outline:hover {
            background: var(--primary-navy);
            color: var(--white);
        }

        /* Form Section */
        .form-section {
            background: var(--white);
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            border: 1px solid var(--neutral-200);
            border-left: 6px solid var(--accent-teal);
        }

        .form-section h3 {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--primary-navy);
            margin-bottom: 8px;
            font-family: 'Roboto', sans-serif;
        }

        .form-section .subtitle {
            color: var(--neutral-600);
            margin-bottom: 32px;
            font-size: 1.1rem;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: var(--neutral-700);
            margin-bottom: 8px;
            font-size: 15px;
        }

        .form-control {
            width: 100%;
            padding: 14px 16px;
            border: 2px solid var(--neutral-300);
            border-radius: 8px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: var(--white);
            font-family: 'Source Sans Pro', sans-serif;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
        }

        .form-control::placeholder {
            color: var(--neutral-400);
        }

        /* Info Tabs */
        .info-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 24px;
            border-bottom: 2px solid var(--neutral-200);
            padding-bottom: 0;
        }

        .tab-btn {
            padding: 12px 20px;
            background: transparent;
            border: none;
            color: var(--neutral-600);
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
            font-family: 'Source Sans Pro', sans-serif;
        }

        .tab-btn:hover,
        .tab-btn.active {
            color: var(--primary-navy);
            border-bottom-color: var(--accent-blue);
            background: var(--neutral-50);
        }

        .info-content {
            background: var(--neutral-50);
            border: 1px solid var(--neutral-200);
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
            display: none;
        }

        .info-content.active {
            display: block;
        }

        .info-content p {
            color: var(--neutral-700);
            margin: 0;
            line-height: 1.7;
        }

        /* Status Badge */
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            background: var(--accent-green);
            color: var(--white);
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.25rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .card-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 20px 15px;
            }
            
            .form-section {
                padding: 24px;
            }
            
            .header {
                margin: -20px -15px 30px -15px;
                padding: 40px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <h1>Corporate Professional</h1>
                <p>Enterprise-grade cloud automation platform</p>
            </div>
        </div>

        <!-- Navigation -->
        <div class="nav-container">
            <nav class="nav-corporate">
                <a href="#" class="logo">HypatIa <span class="status-badge">Enterprise</span></a>
                <ul class="nav-links">
                    <li><a href="#" class="active">Inventory</a></li>
                    <li><a href="#">Compliance</a></li>
                    <li><a href="#">Assessment</a></li>
                    <li><a href="#">Migration</a></li>
                    <li><a href="#">Glide</a></li>
                </ul>
            </nav>
        </div>

        <!-- Cards -->
        <div class="card-grid">
            <div class="card">
                <h3>Cloud Resource Inventory</h3>
                <p>Comprehensive asset discovery and cataloging across Google Cloud Platform, Amazon Web Services, Microsoft Azure, and Oracle Cloud Infrastructure.</p>
                <a href="#" class="btn btn-primary">Start Discovery</a>
            </div>
            
            <div class="card">
                <h3>Compliance Management</h3>
                <p>Automated compliance monitoring and reporting with support for SOC 2, ISO 27001, GDPR, and industry-specific regulations.</p>
                <a href="#" class="btn btn-outline">View Reports</a>
            </div>
            
            <div class="card">
                <h3>Migration Services</h3>
                <p>Strategic cloud migration planning with cost optimization, risk assessment, and automated workload transformation capabilities.</p>
                <a href="#" class="btn btn-primary">Plan Migration</a>
            </div>
        </div>

        <!-- GCP Inventory Form -->
        <div class="form-section">
            <h3>GCP Inventory Management</h3>
            <p class="subtitle">Generate comprehensive inventory reports for your Google Cloud Platform organization</p>
            
            <!-- Info Tabs -->
            <div class="info-tabs">
                <button class="tab-btn active" onclick="showTab('requirements')">Requirements</button>
                <button class="tab-btn" onclick="showTab('process')">Process</button>
                <button class="tab-btn" onclick="showTab('deliverables')">Deliverables</button>
            </div>
            
            <!-- Info Content -->
            <div id="requirements" class="info-content active">
                <p><strong>Access Requirements:</strong><br>
                • Organization-level Browser and Viewer permissions for Cloud Run Service Account<br>
                • Project-level Viewer access when targeting specific projects<br>
                • Valid GCP Organization ID or Project IDs for scope definition</p>
            </div>
            
            <div id="process" class="info-content">
                <p><strong>Inventory Process:</strong><br>
                1. Configure scope using Organization ID, Folder IDs, or Project IDs<br>
                2. Specify regions for Redis and Memcache discovery (optional)<br>
                3. Automated discovery across all GCP services and resources<br>
                4. Data compilation and Excel workbook generation</p>
            </div>
            
            <div id="deliverables" class="info-content">
                <p><strong>Report Deliverables:</strong><br>
                • Comprehensive Excel workbook with detailed resource inventory<br>
                • Service-wise breakdown including Compute, Storage, Networking<br>
                • Cost analysis and resource utilization metrics<br>
                • Security and compliance status overview</p>
            </div>
            
            <!-- Form -->
            <form>
                <div class="form-group">
                    <label for="orgid">Organization ID</label>
                    <input type="text" id="orgid" class="form-control" placeholder="Enter your GCP Organization ID">
                </div>
                
                <div class="form-group">
                    <label for="folderids">Folder IDs (Optional)</label>
                    <input type="text" id="folderids" class="form-control" placeholder="folder-123,folder-456,folder-789">
                </div>
                
                <div class="form-group">
                    <label for="projids">Project IDs (Optional)</label>
                    <input type="text" id="projids" class="form-control" placeholder="project-1,project-2,project-3">
                </div>
                
                <div class="form-group">
                    <label for="regions">Redis/Memcache Regions</label>
                    <input type="text" id="regions" class="form-control" placeholder="us-central1,us-east1,europe-west1">
                </div>
                
                <button type="submit" class="btn btn-primary">Generate Inventory Report</button>
            </form>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            document.querySelectorAll('.info-content').forEach(content => {
                content.classList.remove('active');
            });
            
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
