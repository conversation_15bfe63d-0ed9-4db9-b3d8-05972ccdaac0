<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cyberpunk Tech Design Demo | HypatIa</title>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Color Palette - No Purple */
            --primary-bg: #0a0a0a;
            --secondary-bg: #111111;
            --card-bg: #1a1a1a;
            --accent-cyan: #00ffff;
            --accent-green: #00ff41;
            --accent-orange: #ff6b35;
            --accent-blue: #0099ff;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --text-muted: #666666;
            --border-color: #333333;
            --neon-glow: 0 0 20px;
            --grid-color: rgba(0, 255, 255, 0.1);
        }

        body {
            font-family: 'Rajdhani', sans-serif;
            background: var(--primary-bg);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Animated Grid Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(0, 255, 255, 0.03) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 255, 255, 0.03) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: gridMove 20s linear infinite;
            z-index: -1;
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        /* Floating Particles */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--accent-cyan);
            border-radius: 50%;
            animation: float 15s infinite linear;
            box-shadow: var(--neon-glow) var(--accent-cyan);
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) translateX(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 60px;
            padding: 60px 0;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--accent-cyan), transparent);
            animation: pulse 2s ease-in-out infinite;
        }

        .header h1 {
            font-family: 'Orbitron', monospace;
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 15px;
            background: linear-gradient(45deg, var(--accent-cyan), var(--accent-green));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: var(--neon-glow) var(--accent-cyan);
            animation: slideInDown 1s ease-out;
            letter-spacing: 3px;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.3rem;
            font-weight: 400;
            animation: slideInUp 1s ease-out 0.3s both;
            letter-spacing: 1px;
        }

        /* Navigation */
        .nav-demo {
            margin-bottom: 50px;
            animation: slideInLeft 1s ease-out 0.6s both;
        }

        .nav-cyber {
            background: var(--secondary-bg);
            border: 1px solid var(--border-color);
            border-radius: 0;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .nav-cyber::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--accent-cyan), var(--accent-green));
            transition: left 0.5s ease;
        }

        .nav-cyber:hover::before {
            left: 0;
        }

        .logo-cyber {
            font-family: 'Orbitron', monospace;
            color: var(--accent-cyan);
            font-size: 1.8rem;
            font-weight: 700;
            text-decoration: none;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: var(--neon-glow) var(--accent-cyan);
        }

        .nav-links {
            display: flex;
            gap: 40px;
            list-style: none;
        }

        .nav-links a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 1.1rem;
            padding: 10px 20px;
            border: 1px solid transparent;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(45deg, var(--accent-cyan), transparent);
            transition: width 0.3s ease;
            z-index: -1;
        }

        .nav-links a:hover {
            color: var(--text-primary);
            border-color: var(--accent-cyan);
            box-shadow: var(--neon-glow) var(--accent-cyan);
            transform: translateY(-2px);
        }

        .nav-links a:hover::before {
            width: 100%;
        }

        /* Card Grid */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .card-cyber {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            padding: 30px;
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
            animation: slideInUp 1s ease-out calc(0.9s + var(--delay, 0s)) both;
        }

        .card-cyber::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--accent-cyan), var(--accent-green), var(--accent-orange));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .card-cyber::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(0, 255, 255, 0.05) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .card-cyber:hover {
            transform: translateY(-10px);
            border-color: var(--accent-cyan);
            box-shadow: var(--neon-glow) var(--accent-cyan);
        }

        .card-cyber:hover::before {
            transform: scaleX(1);
        }

        .card-cyber:hover::after {
            opacity: 1;
        }

        .card-cyber h3 {
            font-family: 'Orbitron', monospace;
            color: var(--accent-green);
            font-size: 1.6rem;
            margin-bottom: 15px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .card-cyber p {
            color: var(--text-secondary);
            line-height: 1.7;
            margin-bottom: 25px;
            font-size: 1.1rem;
        }

        /* Buttons */
        .btn-cyber {
            background: transparent;
            border: 2px solid var(--accent-cyan);
            color: var(--accent-cyan);
            padding: 12px 30px;
            font-family: 'Rajdhani', sans-serif;
            font-weight: 600;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }

        .btn-cyber::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--accent-cyan);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .btn-cyber:hover {
            color: var(--primary-bg);
            box-shadow: var(--neon-glow) var(--accent-cyan);
            transform: translateY(-2px);
        }

        .btn-cyber:hover::before {
            left: 0;
        }

        .btn-primary-cyber {
            border-color: var(--accent-green);
            color: var(--accent-green);
        }

        .btn-primary-cyber::before {
            background: var(--accent-green);
        }

        .btn-primary-cyber:hover {
            box-shadow: var(--neon-glow) var(--accent-green);
        }

        /* Form Demo */
        .form-demo {
            margin-bottom: 50px;
            animation: slideInRight 1s ease-out 1.2s both;
        }

        .form-cyber {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            padding: 40px;
            position: relative;
        }

        .form-cyber::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-cyan), var(--accent-green), var(--accent-orange));
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            color: var(--accent-cyan);
            margin-bottom: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .form-control-cyber {
            width: 100%;
            padding: 15px 20px;
            background: var(--secondary-bg);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            font-size: 16px;
            font-family: 'Rajdhani', sans-serif;
            transition: all 0.3s ease;
        }

        .form-control-cyber::placeholder {
            color: var(--text-muted);
        }

        .form-control-cyber:focus {
            outline: none;
            border-color: var(--accent-cyan);
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
            background: var(--primary-bg);
        }

        /* Animations */
        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 0.5;
                transform: translateX(-50%) scaleX(1);
            }
            50% {
                opacity: 1;
                transform: translateX(-50%) scaleX(1.2);
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .card-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .container {
                padding: 15px;
            }
            
            .nav-cyber,
            .form-cyber {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Floating Particles -->
    <div class="particles" id="particles"></div>
    
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>CYBERPUNK TECH</h1>
            <p>Next-Generation Cloud Automation Interface</p>
        </div>

        <!-- Navigation Demo -->
        <div class="nav-demo">
            <nav class="nav-cyber">
                <a href="#" class="logo-cyber">HypatIa</a>
                <ul class="nav-links">
                    <li><a href="#">Inventory</a></li>
                    <li><a href="#">Compliance</a></li>
                    <li><a href="#">Assessment</a></li>
                    <li><a href="#">Migration</a></li>
                    <li><a href="#">Glide</a></li>
                </ul>
            </nav>
        </div>

        <!-- Card Grid -->
        <div class="card-grid">
            <div class="card-cyber" style="--delay: 0s">
                <h3>Cloud Inventory</h3>
                <p>Advanced multi-cloud resource discovery and cataloging system with real-time synchronization across GCP, AWS, Azure, and Oracle Cloud platforms.</p>
                <a href="#" class="btn-primary-cyber">Initialize Scan</a>
            </div>
            
            <div class="card-cyber" style="--delay: 0.2s">
                <h3>Security Matrix</h3>
                <p>Automated compliance monitoring and threat detection with AI-powered security analytics for comprehensive cloud infrastructure protection.</p>
                <a href="#" class="btn-cyber">Access Matrix</a>
            </div>
            
            <div class="card-cyber" style="--delay: 0.4s">
                <h3>Migration Engine</h3>
                <p>Intelligent cloud migration orchestration with predictive cost analysis and zero-downtime deployment strategies for enterprise workloads.</p>
                <a href="#" class="btn-primary-cyber">Launch Engine</a>
            </div>
        </div>

        <!-- Form Demo -->
        <div class="form-demo">
            <div class="form-cyber">
                <h3 style="color: var(--accent-green); margin-bottom: 30px; font-family: 'Orbitron', monospace; font-size: 1.8rem; text-transform: uppercase; letter-spacing: 2px;">System Access Terminal</h3>
                <form>
                    <div class="form-group">
                        <label for="project">Project Identifier</label>
                        <input type="text" id="project" class="form-control-cyber" placeholder="Enter project ID or organization code">
                    </div>
                    <div class="form-group">
                        <label for="region">Target Region</label>
                        <input type="text" id="region" class="form-control-cyber" placeholder="us-central1, eu-west1, ap-southeast1">
                    </div>
                    <div class="form-group">
                        <label for="service">Service Protocol</label>
                        <select class="form-control-cyber">
                            <option>GCP Inventory Scan</option>
                            <option>AWS Resource Discovery</option>
                            <option>Azure Asset Mapping</option>
                            <option>Oracle Cloud Analysis</option>
                        </select>
                    </div>
                    <button type="submit" class="btn-primary-cyber">Execute Command</button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Create floating particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            
            // Parallax effect for grid
            document.addEventListener('mousemove', function(e) {
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;
                
                document.body.style.backgroundPosition = `${x * 20}px ${y * 20}px`;
            });

            // Card hover effects with sound simulation
            const cards = document.querySelectorAll('.card-cyber');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    // Add subtle vibration effect
                    this.style.animation = 'none';
                    setTimeout(() => {
                        this.style.animation = '';
                    }, 10);
                });
            });

            // Form enhancements
            const inputs = document.querySelectorAll('.form-control-cyber');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.style.transform = 'translateX(5px)';
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.style.transform = 'translateX(0)';
                });
            });
        });
    </script>
</body>
</html>
