<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HypatIa Cloud Dashboard | Liquid Glass</title>
    <link rel="icon" sizes="180x180" href="./static/assets/images/hypatia.png" />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    
    <!-- Custom Liquid Glass CSS -->
    <link rel="stylesheet" href="./static/styles/liquid-glass.css" />
    
    <style>
        @font-face {
            font-family: 'HeilHydra';
            src: url('./static/assets/fonts/hydra.ttf') format('truetype');
        }

        /* Dashboard-specific styles */
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: #ffffff;
            position: relative;
            overflow-x: hidden;
        }

        /* Dashboard Layout */
        .dashboard-container {
            display: grid;
            grid-template-areas: 
                "sidebar header"
                "sidebar main";
            grid-template-columns: 280px 1fr;
            grid-template-rows: 80px 1fr;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            grid-area: sidebar;
            background: #000000;
            border-right: 1px solid #333333;
            padding: 20px;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            margin-bottom: 40px;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-logo img {
            width: 32px;
            height: 32px;
            margin-right: 12px;
        }

        .sidebar-logo .brand-text {
            font-family: 'HeilHydra', sans-serif;
            font-size: 24px;
            color: #ffffff;
            text-shadow: none;
        }

        .sidebar-logo .beta-text {
            font-size: 8px;
            vertical-align: super;
            color: var(--text-accent-cyan);
        }

        .sidebar-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-nav li {
            margin-bottom: 8px;
        }

        .sidebar-nav a {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: #cccccc;
            text-decoration: none;
            border-radius: var(--radius-md);
            transition: all var(--transition-normal);
            font-weight: 500;
        }

        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            transform: translateX(4px);
        }

        .sidebar-nav a.active {
            background: rgba(255, 255, 255, 0.15);
            border-left: 3px solid var(--text-accent-blue);
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            opacity: 0.7;
        }

        /* Header */
        .header {
            grid-area: header;
            background: #ffffff;
            border-bottom: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000000;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-md);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        /* Main Content */
        .main-content {
            grid-area: main;
            padding: 30px;
            overflow-y: auto;
            background: #ffffff;
        }

        /* Dashboard Cards */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-lg);
            padding: 24px;
            box-shadow: var(--shadow-glass), var(--shadow-inset);
            transition: all var(--transition-normal);
        }

        .dashboard-card:hover {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-4px);
            box-shadow: var(--shadow-glass-hover), var(--shadow-inset);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #000000;
        }

        .card-icon {
            width: 24px;
            height: 24px;
            opacity: 0.7;
        }

        .card-value {
            font-size: 2rem;
            font-weight: 700;
            color: #000000;
            margin-bottom: 8px;
        }

        .card-description {
            color: #666666;
            font-size: 0.9rem;
        }

        /* Status indicators */
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-active { background: var(--text-accent-green); }
        .status-warning { background: var(--text-accent-orange); }
        .status-error { background: var(--text-accent-red); }
        .status-info { background: var(--text-accent-blue); }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 30px;
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: var(--radius-md);
            padding: 16px;
            color: #000000;
            text-decoration: none;
            transition: all var(--transition-normal);
            text-align: center;
            font-weight: 500;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: var(--shadow-glass);
            color: #000000;
            text-decoration: none;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-areas: 
                    "header"
                    "main";
                grid-template-columns: 1fr;
                grid-template-rows: 80px 1fr;
            }
            
            .sidebar {
                display: none;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
        }

        @keyframes gridMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }
    </style>
</head>

<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-logo">
                <img src="./static/assets/images/hypatia.png" alt="HypatIa">
                <span class="brand-text">HypatIa<sup class="beta-text">BETA</sup></span>
            </div>
            
            <nav>
                <ul class="sidebar-nav">
                    <li><a href="#" class="active">
                        <span class="nav-icon">📊</span>
                        Dashboard
                    </a></li>
                    <li><a href="#">
                        <span class="nav-icon">📦</span>
                        Inventory
                    </a></li>
                    <li><a href="#">
                        <span class="nav-icon">🛡️</span>
                        Compliance
                    </a></li>
                    <li><a href="#">
                        <span class="nav-icon">📋</span>
                        Assessment
                    </a></li>
                    <li><a href="#">
                        <span class="nav-icon">🚀</span>
                        Migration
                    </a></li>
                    <li><a href="#">
                        <span class="nav-icon">⚡</span>
                        Glide
                    </a></li>
                </ul>
            </nav>
        </aside>

        <!-- Header -->
        <header class="header">
            <h1 class="header-title">Cloud Operations Dashboard</h1>
            <div class="header-actions">
                <div class="user-profile">
                    <div class="user-avatar">U</div>
                    <span style="color: #666666;">Admin User</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Stats -->
            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <div class="card-header">
                        <span class="card-title">Total Resources</span>
                        <span class="card-icon">☁️</span>
                    </div>
                    <div class="card-value" style="color: var(--text-accent-blue);">2,847</div>
                    <div class="card-description">
                        <span class="status-indicator status-active"></span>
                        Across all cloud platforms
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <span class="card-title">Compliance Score</span>
                        <span class="card-icon">🛡️</span>
                    </div>
                    <div class="card-value" style="color: var(--text-accent-green);">94%</div>
                    <div class="card-description">
                        <span class="status-indicator status-active"></span>
                        Security standards met
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <span class="card-title">Active Projects</span>
                        <span class="card-icon">📁</span>
                    </div>
                    <div class="card-value" style="color: var(--text-accent-cyan);">156</div>
                    <div class="card-description">
                        <span class="status-indicator status-info"></span>
                        Currently monitored
                    </div>
                </div>

                <div class="dashboard-card">
                    <div class="card-header">
                        <span class="card-title">Cost Optimization</span>
                        <span class="card-icon">💰</span>
                    </div>
                    <div class="card-value" style="color: var(--text-accent-orange);">$12.4K</div>
                    <div class="card-description">
                        <span class="status-indicator status-warning"></span>
                        Potential monthly savings
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <h3 style="color: #000000; margin-bottom: 20px; font-weight: 600;">Quick Actions</h3>
            <div class="quick-actions">
                <a href="#" class="action-btn">
                    📦 Generate GCP Inventory
                </a>
                <a href="#" class="action-btn">
                    🔍 Run Compliance Scan
                </a>
                <a href="#" class="action-btn">
                    📊 Create Assessment
                </a>
                <a href="#" class="action-btn">
                    🚀 Plan Migration
                </a>
            </div>

            <!-- Recent Activity -->
            <div class="dashboard-card" style="margin-top: 20px;">
                <div class="card-header">
                    <span class="card-title">Recent Activity</span>
                    <span class="card-icon">📈</span>
                </div>
                <div style="color: #666666;">
                    <div style="padding: 12px 0; border-bottom: 1px solid rgba(0,0,0,0.1);">
                        <span class="status-indicator status-active"></span>
                        GCP Inventory completed for org-12345
                    </div>
                    <div style="padding: 12px 0; border-bottom: 1px solid rgba(0,0,0,0.1);">
                        <span class="status-indicator status-info"></span>
                        AWS compliance scan initiated
                    </div>
                    <div style="padding: 12px 0;">
                        <span class="status-indicator status-warning"></span>
                        Azure cost optimization recommendations available
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    
    <script>
        // Dashboard interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar navigation
            const navLinks = document.querySelectorAll('.sidebar-nav a');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    navLinks.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Card hover effects
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.background = 'rgba(255, 255, 255, 0.12)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.background = 'rgba(255, 255, 255, 0.08)';
                });
            });

            // Animate numbers on load
            const values = document.querySelectorAll('.card-value');
            values.forEach(value => {
                const text = value.textContent;
                const number = parseInt(text.replace(/[^\d]/g, ''));
                if (number) {
                    let current = 0;
                    const increment = number / 50;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= number) {
                            current = number;
                            clearInterval(timer);
                        }
                        value.textContent = text.replace(number, Math.floor(current));
                    }, 30);
                }
            });
        });
    </script>
</body>
</html>
