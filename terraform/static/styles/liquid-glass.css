/* ===================================
   LIQUID GLASS CSS FRAMEWORK
   Modern Glassmorphism Design System
   =================================== */

/* CSS Custom Properties */
:root {
    /* Glass Colors */
    --glass-bg-primary: rgba(255, 255, 255, 0.1);
    --glass-bg-secondary: rgba(255, 255, 255, 0.15);
    --glass-bg-subtle: rgba(255, 255, 255, 0.05);
    --glass-bg-strong: rgba(255, 255, 255, 0.2);
    
    /* Border Colors */
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-border-hover: rgba(255, 255, 255, 0.3);
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-muted: rgba(255, 255, 255, 0.6);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    
    /* Shadows */
    --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-glass-hover: 0 20px 60px rgba(0, 0, 0, 0.2);
    --shadow-inset: inset 0 1px 0 rgba(255, 255, 255, 0.2);
    
    /* Blur Values */
    --blur-light: blur(15px);
    --blur-medium: blur(20px);
    --blur-heavy: blur(25px);
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 20px;
    --radius-xl: 24px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.4s ease;
}

/* Base Glass Classes */
.glass {
    background: var(--glass-bg-primary);
    backdrop-filter: var(--blur-medium);
    -webkit-backdrop-filter: var(--blur-medium);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-glass), var(--shadow-inset);
    transition: all var(--transition-normal);
}

.glass-subtle {
    background: var(--glass-bg-subtle);
    backdrop-filter: var(--blur-light);
    -webkit-backdrop-filter: var(--blur-light);
}

.glass-strong {
    background: var(--glass-bg-strong);
    backdrop-filter: var(--blur-heavy);
    -webkit-backdrop-filter: var(--blur-heavy);
}

.glass-hover:hover {
    background: var(--glass-bg-secondary);
    border-color: var(--glass-border-hover);
    box-shadow: var(--shadow-glass-hover), var(--shadow-inset);
    transform: translateY(-2px);
}

/* Background Patterns */
.bg-liquid {
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.bg-liquid::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(30px) rotate(240deg); }
}

/* Navigation Components */
.nav-glass {
    background: var(--glass-bg-primary);
    backdrop-filter: var(--blur-medium);
    -webkit-backdrop-filter: var(--blur-medium);
    border-bottom: 1px solid var(--glass-border);
    transition: all var(--transition-normal);
}

.nav-glass.scrolled {
    background: var(--glass-bg-secondary);
    box-shadow: var(--shadow-glass);
}

.nav-item-glass {
    color: var(--text-secondary);
    padding: 12px 20px;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    text-decoration: none;
    display: inline-block;
}

.nav-item-glass:hover,
.nav-item-glass.active {
    background: var(--glass-bg-subtle);
    color: var(--text-primary);
    transform: translateY(-1px);
}

/* Button Components */
.btn-glass {
    background: var(--glass-bg-primary);
    backdrop-filter: var(--blur-medium);
    -webkit-backdrop-filter: var(--blur-medium);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    padding: 12px 24px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    display: inline-block;
}

.btn-glass:hover {
    background: var(--glass-bg-secondary);
    border-color: var(--glass-border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glass);
}

.btn-gradient {
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--radius-md);
    color: var(--text-primary);
    padding: 12px 24px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.btn-gradient.success {
    background: var(--gradient-success);
    box-shadow: 0 4px 15px rgba(67, 233, 123, 0.4);
}

.btn-gradient.warning {
    background: var(--gradient-warning);
    box-shadow: 0 4px 15px rgba(250, 112, 154, 0.4);
}

/* Form Components */
.form-glass {
    background: var(--glass-bg-primary);
    backdrop-filter: var(--blur-medium);
    -webkit-backdrop-filter: var(--blur-medium);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: 30px;
    box-shadow: var(--shadow-glass), var(--shadow-inset);
}

.input-glass {
    background: var(--glass-bg-subtle);
    backdrop-filter: var(--blur-light);
    -webkit-backdrop-filter: var(--blur-light);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    padding: 15px 20px;
    width: 100%;
    transition: all var(--transition-normal);
    font-size: 16px;
}

.input-glass::placeholder {
    color: var(--text-muted);
}

.input-glass:focus {
    outline: none;
    background: var(--glass-bg-primary);
    border-color: var(--glass-border-hover);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

.select-glass {
    background: var(--glass-bg-subtle);
    backdrop-filter: var(--blur-light);
    -webkit-backdrop-filter: var(--blur-light);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    padding: 15px 20px;
    width: 100%;
    transition: all var(--transition-normal);
}

/* Card Components */
.card-glass {
    background: var(--glass-bg-primary);
    backdrop-filter: var(--blur-medium);
    -webkit-backdrop-filter: var(--blur-medium);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: 30px;
    box-shadow: var(--shadow-glass), var(--shadow-inset);
    transition: all var(--transition-slow);
}

.card-glass:hover {
    background: var(--glass-bg-secondary);
    border-color: var(--glass-border-hover);
    transform: translateY(-5px) scale(1.01);
    box-shadow: var(--shadow-glass-hover), var(--shadow-inset);
}

/* Tab Components */
.tab-glass {
    background: var(--glass-bg-subtle);
    backdrop-filter: var(--blur-light);
    -webkit-backdrop-filter: var(--blur-light);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    padding: 12px 24px;
    color: var(--text-secondary);
    transition: all var(--transition-normal);
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.tab-glass:hover,
.tab-glass.active {
    background: var(--glass-bg-primary);
    color: var(--text-primary);
    transform: translateY(-1px);
}

/* Modal Components */
.modal-glass {
    background: var(--glass-bg-secondary);
    backdrop-filter: var(--blur-heavy);
    -webkit-backdrop-filter: var(--blur-heavy);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-glass-hover), var(--shadow-inset);
}

.modal-overlay {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: var(--blur-light);
    -webkit-backdrop-filter: var(--blur-light);
}

/* Text Styles */
.text-glass {
    color: var(--text-primary);
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.text-glass-secondary {
    color: var(--text-secondary);
}

.text-glass-muted {
    color: var(--text-muted);
}

/* Animation Classes */
.animate-slide-in {
    animation: slideIn 0.6s ease-out;
}

.animate-fade-in {
    animation: fadeIn 0.8s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Utility Classes */
.blur-bg {
    backdrop-filter: var(--blur-medium);
    -webkit-backdrop-filter: var(--blur-medium);
}

.rounded-glass {
    border-radius: var(--radius-lg);
}

.shadow-glass {
    box-shadow: var(--shadow-glass);
}

.border-glass {
    border: 1px solid var(--glass-border);
}

/* Loading and Progress Components */
.loading-glass {
    background: var(--glass-bg-secondary);
    backdrop-filter: var(--blur-heavy);
    -webkit-backdrop-filter: var(--blur-heavy);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: 40px;
    text-align: center;
    box-shadow: var(--shadow-glass-hover), var(--shadow-inset);
}

.spinner-glass {
    width: 40px;
    height: 40px;
    border: 3px solid var(--glass-border);
    border-top: 3px solid var(--text-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.progress-glass {
    background: var(--glass-bg-subtle);
    border-radius: var(--radius-md);
    height: 8px;
    overflow: hidden;
    backdrop-filter: var(--blur-light);
    -webkit-backdrop-filter: var(--blur-light);
}

.progress-bar-glass {
    background: var(--gradient-primary);
    height: 100%;
    border-radius: var(--radius-md);
    transition: width var(--transition-normal);
}

/* Alert Components */
.alert-glass {
    background: var(--glass-bg-primary);
    backdrop-filter: var(--blur-medium);
    -webkit-backdrop-filter: var(--blur-medium);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    padding: 16px 20px;
    margin: 16px 0;
    box-shadow: var(--shadow-glass);
}

.alert-glass.success {
    border-left: 4px solid #43e97b;
    background: rgba(67, 233, 123, 0.1);
}

.alert-glass.warning {
    border-left: 4px solid #fee140;
    background: rgba(254, 225, 64, 0.1);
}

.alert-glass.error {
    border-left: 4px solid #f5576c;
    background: rgba(245, 87, 108, 0.1);
}

.alert-glass.info {
    border-left: 4px solid #4facfe;
    background: rgba(79, 172, 254, 0.1);
}

/* Dropdown Components */
.dropdown-glass {
    background: var(--glass-bg-primary);
    backdrop-filter: var(--blur-medium);
    -webkit-backdrop-filter: var(--blur-medium);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-glass);
    padding: 8px 0;
    min-width: 200px;
}

.dropdown-item-glass {
    padding: 12px 20px;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    cursor: pointer;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.dropdown-item-glass:hover {
    background: var(--glass-bg-subtle);
    color: var(--text-primary);
}

/* Table Components */
.table-glass {
    background: var(--glass-bg-primary);
    backdrop-filter: var(--blur-medium);
    -webkit-backdrop-filter: var(--blur-medium);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-glass);
}

.table-glass th {
    background: var(--glass-bg-secondary);
    color: var(--text-primary);
    padding: 16px 20px;
    font-weight: 600;
    border-bottom: 1px solid var(--glass-border);
}

.table-glass td {
    padding: 16px 20px;
    color: var(--text-secondary);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.table-glass tr:hover {
    background: var(--glass-bg-subtle);
}

/* Badge Components */
.badge-glass {
    background: var(--glass-bg-primary);
    backdrop-filter: var(--blur-light);
    -webkit-backdrop-filter: var(--blur-light);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-sm);
    padding: 4px 12px;
    font-size: 12px;
    font-weight: 500;
    color: var(--text-primary);
    display: inline-block;
}

.badge-glass.success {
    background: rgba(67, 233, 123, 0.2);
    border-color: #43e97b;
    color: #43e97b;
}

.badge-glass.warning {
    background: rgba(254, 225, 64, 0.2);
    border-color: #fee140;
    color: #fee140;
}

.badge-glass.error {
    background: rgba(245, 87, 108, 0.2);
    border-color: #f5576c;
    color: #f5576c;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-glass,
    .card-glass {
        padding: 20px;
    }

    .btn-glass,
    .btn-gradient {
        padding: 10px 20px;
        font-size: 14px;
    }

    .input-glass {
        padding: 12px 16px;
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .table-glass th,
    .table-glass td {
        padding: 12px 16px;
    }

    .nav-item-glass {
        padding: 10px 16px;
    }
}

@media (max-width: 480px) {
    :root {
        --radius-lg: 16px;
        --radius-xl: 20px;
    }

    .form-glass,
    .card-glass {
        padding: 15px;
    }

    .loading-glass {
        padding: 30px 20px;
    }

    .dropdown-glass {
        min-width: 150px;
    }
}
