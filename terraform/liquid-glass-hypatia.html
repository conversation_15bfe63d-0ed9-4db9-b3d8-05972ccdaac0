<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Hybrid Cloud Deployment and Resource Automation | Searce</title>
    <link rel="icon" sizes="180x180" href="./static/assets/images/hypatia.png" />

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    
    <!-- Custom Liquid Glass CSS -->
    <link rel="stylesheet" href="./static/styles/liquid-glass.css" />
    
    <!-- Original CSS for compatibility -->
    <link rel="stylesheet" href="./static/styles/on.css" />
    <link rel="stylesheet" href="./static/styles/app.min.css" type="text/css" media="all" />
    
    <style>
        @font-face {
            font-family: 'HeilHydra';
            src: url('./static/assets/fonts/hydra.ttf') format('truetype');
        }

        /* Override base styles for liquid glass theme */
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow-x: hidden;
        }

        /* Animated background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
            z-index: -1;
        }

        /* Navigation Styles */
        .navbar {
            background: rgba(255, 255, 255, 0.1) !important;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            padding: 15px 0;
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.15) !important;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand img {
            filter: brightness(0) invert(1);
            transition: all 0.3s ease;
        }

        .brand-container {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .animated-logo {
            display: flex;
            align-items: center;
            animation: slideInLeft 1s ease-out;
            text-decoration: none;
        }

        .logo {
            width: 30px;
            height: 30px;
            animation: slideInLeft 1s ease-out 0.4s both;
        }

        .brand-text {
            font-family: 'HeilHydra', sans-serif;
            font-size: 28px;
            margin-left: 8px;
            color: white;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            animation: slideInLeft 1s ease-out 0.6s both;
        }

        .beta-text {
            font-size: 10px;
            vertical-align: super;
            line-height: 20%;
            animation: slideInLeft 1s ease-out 0.8s both;
        }

        /* Main Navigation Buttons */
        .choice-main {
            display: flex;
            gap: 15px;
            align-items: center;
            animation: slideInRight 1s ease-out 0.5s both;
        }

        .choice-btn {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 14px;
        }

        .choice-btn:hover,
        .choice-btn.active {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .choice-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-color: rgba(255, 255, 255, 0.4);
        }

        /* Video Icon */
        .icon {
            animation: slideInRight 1s ease-out 0.7s both;
        }

        .icon img {
            filter: brightness(0) invert(1);
            transition: all 0.3s ease;
            border-radius: 8px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .icon img:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        /* Main Content Area */
        .main-content {
            padding-top: 120px;
            min-height: 100vh;
        }

        .section-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            margin: 20px;
            padding: 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2);
            animation: slideInUp 1s ease-out 1s both;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .choice-main {
                flex-direction: column;
                gap: 10px;
            }
            
            .choice-btn {
                padding: 10px 16px;
                font-size: 13px;
            }
            
            .brand-text {
                font-size: 24px;
            }
            
            .section-container {
                margin: 10px;
                padding: 20px;
            }
        }

        /* Animations */
        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(50px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(120deg); }
            66% { transform: translateY(30px) rotate(240deg); }
        }

        /* Hidden sections */
        .hidden {
            display: none !important;
        }

        /* Grid layout for header */
        .header-grid {
            display: grid;
            grid-template-columns: auto 1fr auto;
            align-items: center;
            width: 100%;
            gap: 20px;
        }

        .nav-center {
            display: flex;
            justify-content: center;
        }

        @media (max-width: 992px) {
            .header-grid {
                grid-template-columns: auto 1fr;
                gap: 15px;
            }
            
            .nav-center {
                justify-content: flex-end;
            }
            
            .choice-main {
                flex-wrap: wrap;
                gap: 8px;
            }
        }
    </style>
</head>

<body id="the-body">
    <nav id="navi" class="navbar fixed-top navbar-expand-lg">
        <div class="container">
            <div class="header-grid">
                <!-- Logo Section -->
                <div class="brand-container">
                    <a class="navbar-brand" href="/">
                        <img style="height:30px" src="./static/assets/images/logo.svg" alt="Searce" />
                    </a>
                    <a class="animated-logo" href="/">
                        <img src="./static/assets/images/hypatia.png" alt="Hypatia" class="logo">
                        <span class="brand-text">HypatIa<sup class="beta-text">BETA</sup></span>
                    </a>
                </div>
                
                <!-- Navigation Menu -->
                <div class="nav-center">
                    <div class="choice-main" id="menu">
                        <button id="buto3" class="choice-btn" onclick="switchCont(3)">Assessment</button>
                        <button id="buto4" class="choice-btn" onclick="switchCont(4)">Migration</button>
                        <button id="buto1" class="choice-btn active" onclick="switchCont(1)">Inventory</button>
                        <button id="buto2" class="choice-btn" onclick="switchCont(2)">Compliance</button>
                        <button id="buto5" class="choice-btn" onclick="switchCont(5)">Glide</button>
                    </div>
                </div>
                
                <!-- Video Icon -->
                <div class="icon" id="icon">
                    <a href="/video">
                        <img style="height:30px" src="./static/assets/images/video.png" id="icon-image" alt="Video" />
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <main id="home" class="main-content">
        <div class="section-container">
            <div id="astirth" class="tab-content">
                <!-- Inventory Section -->
                <div id="div1" class="tab-pane fade in active show">
                    <div class="nav-container animate-fade-in">
                        <h2 class="text-glass" style="text-align: center; margin-bottom: 30px;">Cloud Inventory Management</h2>
                        <p class="text-glass-secondary" style="text-align: center; margin-bottom: 40px;">
                            Generate comprehensive inventory reports across multiple cloud platforms
                        </p>

                        <!-- Inventory Tabs -->
                        <ul class="nav nav-tabs" style="border: none; justify-content: center; margin-bottom: 40px;">
                            <li style="margin: 0 10px;">
                                <a class="tab-glass active" data-toggle="tab" href="#software">
                                    <img src="./static/assets/images/azinv1.png" style="width: 24px; height: 24px; margin-right: 8px;" />
                                    <span>GCP Inventory</span>
                                </a>
                            </li>
                            <li style="margin: 0 10px;">
                                <a class="tab-glass" data-toggle="tab" href="#aws1">
                                    <img src="./static/assets/images/awsinv1.png" style="width: 24px; height: 24px; margin-right: 8px;" />
                                    <span>AWS Inventory</span>
                                </a>
                            </li>
                            <li style="margin: 0 10px;">
                                <a class="tab-glass" data-toggle="tab" href="#ari">
                                    <img src="./static/assets/images/inv1.png" style="width: 24px; height: 24px; margin-right: 8px;" />
                                    <span>Azure Inventory</span>
                                </a>
                            </li>
                            <li style="margin: 0 10px;">
                                <a class="tab-glass" data-toggle="tab" href="#oci_inv">
                                    <img src="./static/assets/images/oci_inv1.png" style="width: 24px; height: 24px; margin-right: 8px;" />
                                    <span>Oracle Inventory</span>
                                </a>
                            </li>
                            <li style="margin: 0 10px;">
                                <a class="tab-glass" data-toggle="tab" href="#gke_dragon">
                                    <img src="./static/assets/images/google-gke.svg" style="width: 24px; height: 24px; margin-right: 8px;" />
                                    <span>GKE Inventory</span>
                                </a>
                            </li>
                            <li style="margin: 0 10px;">
                                <a class="tab-glass" data-toggle="tab" href="#eks">
                                    <img src="./static/assets/images/eksinv.png" style="width: 24px; height: 24px; margin-right: 8px;" />
                                    <span>EKS Inventory</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <div class="tab-content">
                        <!-- GCP Inventory Tab -->
                        <div id="software" class="tab-pane fade in active show">
                            <div class="card-glass animate-slide-in">
                                <h3 class="text-glass" style="margin-bottom: 20px;">
                                    Generate GCP Inventory Workbook for an organisation
                                    <span style="color: #29c8e1"> by giving org id</span>
                                </h3>

                                <!-- Info Tabs -->
                                <div class="info-tabs" style="margin-bottom: 30px;">
                                    <button class="btn-glass" onclick="openCity(event, 'soft_req')" style="margin-right: 10px;">Requirements</button>
                                    <button class="btn-glass" onclick="openCity(event, 'soft_steps')" style="margin-right: 10px;">Steps</button>
                                    <button class="btn-glass" onclick="openCity(event, 'soft_out')" style="margin-right: 10px;">Output</button>
                                    <button class="btn-glass" onclick="openCity(event, 'soft_vid')" style="margin-right: 10px;">Video</button>
                                    <button class="btn-glass" onclick="openCity(event, 'soft_wn')">What Next?</button>
                                </div>

                                <!-- Info Content -->
                                <div id="soft_req" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">
                                        1. Needs <span class="text-glass" style="font-weight: bold">Browser</span> and <span class="text-glass" style="font-weight: bold">Viewer</span> access at Organization Level for Cloud Run Service Account
                                        <span style="color: #4facfe"><EMAIL></span><br>
                                        2. Will also work with <span class="text-glass" style="font-weight: bold">Viewer</span> access at Project level when projects have been selected.<br>
                                    </p>
                                    <div style="text-align: center; margin-top: 20px;">
                                        <img src="./static/assets/images/permissions.png" style="max-width: 100%; border-radius: 12px;" />
                                    </div>
                                </div>

                                <div id="soft_steps" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">
                                        1. Fill in at least one field based on your requirements.<br>
                                        2. Enter GCP Organization ID to create an inventory for the whole organization.<br>
                                        3. Enter Folder IDs (comma-separated) to create an inventory for specific folders.<br>
                                        4. Enter Project IDs (comma-separated) to create an inventory for specific projects.<br>
                                        5. To include Redis, provide comma separated regions. Default region is us-central1.
                                    </p>
                                </div>

                                <div id="soft_out" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <a href="https://docs.google.com/spreadsheets/d/1_D9pkJiBefJ1MXF9KGN97sz_h1qmYFT-/view?usp=sharing&ouid=105634715941788046732&rtpof=true&sd=true"
                                       target="_blank" class="btn-gradient" style="margin-bottom: 20px;">Sample Output</a>
                                    <div style="text-align: center;">
                                        <img src="./static/assets/images/gcpinvent1.png" style="max-width: 100%; border-radius: 12px;" />
                                    </div>
                                </div>

                                <div id="soft_vid" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <iframe src="https://drive.google.com/file/d/1NZ96Cgwku385dW0A8MSAssljRudJH3n3/preview"
                                            width="100%" height="420" allow="autoplay; fullscreen;" style="border-radius: 12px;"></iframe>
                                </div>

                                <div id="soft_wn" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">No further action is required.</p>
                                </div>

                                <!-- Form Section -->
                                <div class="form-glass" style="margin-top: 30px;">
                                    <form id="form-tilt" action="inventory" method="post" enctype="multipart/form-data"
                                          onsubmit="addUUID(this); revealText(this, 'tilt','');">
                                        <input type="hidden" id="call" name="call" value="gcp">

                                        <div class="form-group" style="margin-bottom: 25px;">
                                            <label class="text-glass" style="margin-bottom: 8px;">Org ID</label>
                                            <input type="text" placeholder="Enter Org Id" id="orgid" name="orgid" class="input-glass">
                                        </div>

                                        <div class="form-group" style="margin-bottom: 25px;">
                                            <label class="text-glass" style="margin-bottom: 8px;">Folder ID</label>
                                            <input type="text" placeholder="Folderid" id="folderids" name="folderids" class="input-glass">
                                        </div>

                                        <div class="form-group" style="margin-bottom: 25px;">
                                            <label class="text-glass" style="margin-bottom: 8px;">Allowed Project IDs</label>
                                            <input type="text" placeholder="Proj1,Proj2,Proj3" id="projids" name="projids" class="input-glass">
                                        </div>

                                        <div class="form-group" style="margin-bottom: 25px;">
                                            <label class="text-glass" style="margin-bottom: 8px;">Redis/Memcache Regions</label>
                                            <input type="text" placeholder="us-central1,us-east1..." id="redis-reg" name="redis-reg" class="input-glass">
                                        </div>

                                        <button id="gcpin" type="submit" class="btn-gradient" disabled>Generate</button>
                                    </form>

                                    <div id="text-block-container-tilt" style="filter:none"></div>
                                </div>
                            </div>
                        </div>

                        <!-- AWS Inventory Tab -->
                        <div id="aws1" class="tab-pane fade">
                            <div class="card-glass animate-slide-in">
                                <h3 class="text-glass" style="margin-bottom: 20px;">
                                    Generate AWS Inventory Workbook for an organisation
                                    <span style="color: #29c8e1"> by providing ARN</span>
                                </h3>

                                <!-- Info Tabs -->
                                <div class="info-tabs" style="margin-bottom: 30px;">
                                    <button class="btn-glass" onclick="openCity(event, 'aws1_req')" style="margin-right: 10px;">Requirements</button>
                                    <button class="btn-glass" onclick="openCity(event, 'aws1_steps')" style="margin-right: 10px;">Steps</button>
                                    <button class="btn-glass" onclick="openCity(event, 'aws1_out')" style="margin-right: 10px;">Output</button>
                                    <button class="btn-glass" onclick="openCity(event, 'aws1_vid')" style="margin-right: 10px;">Video</button>
                                    <button class="btn-glass" onclick="openCity(event, 'aws1_wn')">What Next?</button>
                                </div>

                                <!-- Info Content -->
                                <div id="aws1_req" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">
                                        1. Cloud Run Google Service Account <span style="color: #4facfe"><EMAIL></span>
                                        requires AWS IAM Policy <span class="text-glass" style="font-weight: bold">arn:aws:iam::aws:policy/ReadOnlyAccess</span> on AWS project.
                                    </p>
                                </div>

                                <div id="aws1_steps" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">
                                        1. Download <a href="https://storage.googleapis.com/hypatia-public-artifacts/hypatia-aws-eks-cfn.yaml"
                                           target="_blank" class="text-glass" style="color: #4facfe;">AWS Cloudformation file</a> and use the
                                        <a href="https://docs.google.com/document/d/1NtyMtZHWPc5eaZn5fcXBqYEHDCSyd87NrDFHgsAT6GY/view"
                                           target="_blank" class="text-glass" style="color: #4facfe;">instructions</a> or
                                        <a href="./static/assets/pdfs/aws-instructions-new.pdf" target="_blank" class="text-glass" style="color: #4facfe;">instructions.pdf</a>.<br>
                                        2. The Stack should create a IAM Role and will output its ARN.<br>
                                        3. You need to supply the ARNs in the form.<br>
                                        4. You can provide comma seperated regions. It is optional.<br>
                                        5. Click Generate.<br>
                                    </p>
                                </div>

                                <div id="aws1_out" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <a href="https://docs.google.com/spreadsheets/d/1tGel4HcGTm5KTpiTSB5Ain4GDZP3687dGzwff_WP1C0/view?usp=drive_link"
                                       target="_blank" class="btn-gradient" style="margin-bottom: 20px;">Sample Output</a>
                                    <div style="text-align: center;">
                                        <img src="./static/assets/images/awsinvent1.png" style="max-width: 100%; border-radius: 12px;" />
                                    </div>
                                </div>

                                <div id="aws1_vid" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <iframe src="https://drive.google.com/file/d/12PsnDsvWwl8ArrsXqgoH6wul35J78DHi/preview"
                                            width="100%" height="420" allow="autoplay; fullscreen;" style="border-radius: 12px;"></iframe>
                                </div>

                                <div id="aws1_wn" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">No further action is required.</p>
                                </div>

                                <!-- Form Section -->
                                <div class="form-glass" style="margin-top: 30px;">
                                    <form id="form-tilt1" action="inventory" method="post" enctype="multipart/form-data"
                                          onsubmit="addUUID(this); revealText(this, 'tilt1','');">
                                        <input type="hidden" id="call" name="call" value="aws">

                                        <div class="form-group" style="margin-bottom: 25px;">
                                            <label class="text-glass" style="margin-bottom: 8px;">ARN</label>
                                            <input type="text" placeholder="Enter ARN" id="arn" name="arn" class="input-glass" required>
                                        </div>

                                        <div class="form-group" style="margin-bottom: 25px;">
                                            <label class="text-glass" style="margin-bottom: 8px;">Regions</label>
                                            <input type="text" placeholder="*" id="regions" name="regions" class="input-glass">
                                        </div>

                                        <button id="awsin" type="submit" class="btn-gradient">Generate</button>
                                    </form>

                                    <div id="text-block-container-tilt1" style="filter:none"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Azure Inventory Tab -->
                        <div id="ari" class="tab-pane fade">
                            <div class="card-glass animate-slide-in">
                                <h3 class="text-glass" style="margin-bottom: 20px;">
                                    Generate Azure Inventory Workbook
                                    <span style="color: #0078d4"> by uploading credentials</span>
                                </h3>

                                <!-- Info Tabs -->
                                <div class="info-tabs" style="margin-bottom: 30px;">
                                    <button class="btn-glass" onclick="openCity(event, 'ari_req')" style="margin-right: 10px;">Requirements</button>
                                    <button class="btn-glass" onclick="openCity(event, 'ari_steps')" style="margin-right: 10px;">Steps</button>
                                    <button class="btn-glass" onclick="openCity(event, 'ari_out')" style="margin-right: 10px;">Output</button>
                                    <button class="btn-glass" onclick="openCity(event, 'ari_vid')" style="margin-right: 10px;">Video</button>
                                    <button class="btn-glass" onclick="openCity(event, 'ari_wn')">What Next?</button>
                                </div>

                                <!-- Info Content -->
                                <div id="ari_req" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">
                                        1. Create a Service Principal with <span class="text-glass" style="font-weight: bold">Reader</span> access.<br>
                                        2. Generate credentials file with required permissions.<br>
                                        3. Upload the credentials file in the form below.
                                    </p>
                                </div>

                                <div id="ari_steps" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">
                                        1. Create Azure Service Principal with Reader permissions.<br>
                                        2. Download the credentials file.<br>
                                        3. Upload the file and click Generate.
                                    </p>
                                </div>

                                <div id="ari_out" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <a href="#" target="_blank" class="btn-gradient" style="margin-bottom: 20px;">Sample Output</a>
                                    <div style="text-align: center;">
                                        <img src="./static/assets/images/azureinvent.png" style="max-width: 100%; border-radius: 12px;" />
                                    </div>
                                </div>

                                <div id="ari_vid" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">Coming soon...</p>
                                </div>

                                <div id="ari_wn" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">Remove the service principal created to fetch the Inventory.</p>
                                </div>

                                <!-- Form Section -->
                                <div class="form-glass" style="margin-top: 30px;">
                                    <form id="form-azure" action="inventory" method="post" enctype="multipart/form-data"
                                          onsubmit="addUUID(this); revealText(this, 'azure','');">
                                        <input type="hidden" id="call" name="call" value="azure">

                                        <div class="form-group" style="margin-bottom: 25px;">
                                            <label class="text-glass" style="margin-bottom: 8px;">Azure Credentials File</label>
                                            <input type="file" id="azure_creds" name="azure_creds" accept=".json" class="input-glass" required>
                                        </div>

                                        <button id="azurein" type="submit" class="btn-gradient">Generate</button>
                                    </form>

                                    <div id="text-block-container-azure" style="filter:none"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Oracle Inventory Tab -->
                        <div id="oci_inv" class="tab-pane fade">
                            <div class="card-glass animate-slide-in">
                                <h3 class="text-glass" style="margin-bottom: 20px;">
                                    Generate Oracle Cloud Inventory
                                    <span style="color: #f5c518"> by uploading OCI credentials export file</span>
                                </h3>

                                <!-- Info Tabs -->
                                <div class="info-tabs" style="margin-bottom: 30px;">
                                    <button class="btn-glass" onclick="openCity(event, 'oci_req')" style="margin-right: 10px;">Requirements</button>
                                    <button class="btn-glass" onclick="openCity(event, 'oci_steps')" style="margin-right: 10px;">Steps</button>
                                    <button class="btn-glass" onclick="openCity(event, 'oci_out')" style="margin-right: 10px;">Output</button>
                                    <button class="btn-glass" onclick="openCity(event, 'oci_vid')" style="margin-right: 10px;">Video</button>
                                    <button class="btn-glass" onclick="openCity(event, 'oci_wn')">What Next?</button>
                                </div>

                                <!-- Info Content -->
                                <div id="oci_req" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">
                                        1. Create a User with <span class="text-glass" style="font-weight: bold">Reader Access</span>.<br>
                                        2. Follow the steps provided in next block to generate <span class="text-glass" style="font-weight: bold">user credentials export file</span>.<br>
                                        3. Specify <span class="text-glass" style="font-weight: bold">Region</span> for the user, as inventory is generated per region.
                                    </p>
                                </div>

                                <div id="oci_steps" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">
                                        1. Get the OCI Credential zip file by following the steps mentioned in
                                        <a href="https://docs.google.com/document/d/1XcAl64pVcYvU9chOKs76nmzW-3K2-EIvFRaunoNF_as/view"
                                           target="_blank" class="text-glass" style="color: #4facfe;">Instructions</a> or
                                        <a href="./static/assets/pdfs/oci-instruction.pdf" target="_blank" class="text-glass" style="color: #4facfe;">instructions.pdf</a><br>
                                        2. Upload the credential zip file and provide the Region and Click Generate.
                                    </p>
                                </div>

                                <div id="oci_out" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <a href="https://docs.google.com/spreadsheets/d/16jy624Of6p0qOlhCFNCIDwp8IMRp1N2nuTEcYVqqBoI/view"
                                       target="_blank" class="btn-gradient" style="margin-bottom: 20px;">Sample Output</a>
                                    <div style="text-align: center;">
                                        <img src="static/assets/images/sample-output-oci-inv.png" style="max-width: 100%; border-radius: 12px;" />
                                    </div>
                                </div>

                                <div id="oci_vid" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">Coming soon...</p>
                                </div>

                                <div id="oci_wn" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">Remove the user created to fetch the Inventory.</p>
                                </div>

                                <!-- Form Section -->
                                <div class="form-glass" style="margin-top: 30px;">
                                    <form id="form-oci" action="/inventory" method="post" enctype="multipart/form-data"
                                          onsubmit="addUUID(this); revealText(this, 'oci','');">
                                        <input type="hidden" id="call" name="call" value="oci">

                                        <div class="form-group" style="margin-bottom: 25px;">
                                            <label class="text-glass" style="margin-bottom: 8px;">OCI Credentials Zip File</label>
                                            <input type="file" id="oci_zip_file" name="oci_zip_file" accept=".zip" class="input-glass" required>
                                        </div>

                                        <div class="form-group" style="margin-bottom: 25px;">
                                            <label class="text-glass" style="margin-bottom: 8px;">OCI Region</label>
                                            <input type="text" placeholder="Enter OCI Region" id="oci_region" name="region"
                                                   value="ap-mumbai-1" class="input-glass" required>
                                        </div>

                                        <button id="oci_generate" type="submit" class="btn-gradient">Generate</button>
                                    </form>

                                    <div id="text-block-container-oci" style="filter:none"></div>
                                </div>
                            </div>
                        </div>

                        <!-- GKE Inventory Tab -->
                        <div id="gke_dragon" class="tab-pane fade">
                            <div class="card-glass animate-slide-in">
                                <h3 class="text-glass" style="margin-bottom: 20px;">
                                    Generate GKE Inventory Sheet for all clusters in project
                                    <span style="color: #F08000"> by giving project id</span>
                                </h3>

                                <!-- Info Tabs -->
                                <div class="info-tabs" style="margin-bottom: 30px;">
                                    <button class="btn-glass" onclick="openCity(event, 'gke_req')" style="margin-right: 10px;">Requirements</button>
                                    <button class="btn-glass" onclick="openCity(event, 'gke_steps')" style="margin-right: 10px;">Steps</button>
                                    <button class="btn-glass" onclick="openCity(event, 'gke_out')" style="margin-right: 10px;">Output</button>
                                    <button class="btn-glass" onclick="openCity(event, 'gke_vid')" style="margin-right: 10px;">Video</button>
                                    <button class="btn-glass" onclick="openCity(event, 'gke_wn')">What Next?</button>
                                </div>

                                <!-- Info Content -->
                                <div id="gke_req" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">
                                        1. Require the Cluster Endpoint to be public for the probe to be successful.<br>
                                        2. <span style="color: #4facfe"><EMAIL></span> needs
                                        <span class="text-glass" style="font-weight: bold">Kubernetes Engine Viewer</span> IAM Role on the project.
                                    </p>
                                </div>

                                <div id="gke_steps" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">
                                        1. Enter the Project ID of the project in which the clusters are present.
                                    </p>
                                </div>

                                <div id="gke_out" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <a href="https://docs.google.com/spreadsheets/d/12khW7WBOeQfYtLIQ7XkTfVpK-39RunbcvY6RkgsU1k0/view?usp=sharing"
                                       target="_blank" class="btn-gradient" style="margin-bottom: 20px;">Sample Output</a>
                                    <div style="text-align: center;">
                                        <img src="./static/assets/images/gkeinvent.png" style="max-width: 100%; border-radius: 12px;" />
                                    </div>
                                </div>

                                <div id="gke_vid" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <iframe src="https://drive.google.com/file/d/1OZ7bOtD2ImZ7xFO-GnZAyMLL4arXSeEE/preview"
                                            width="100%" height="420" allow="autoplay; fullscreen;" style="border-radius: 12px;"></iframe>
                                </div>

                                <div id="gke_wn" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">No further action is required.</p>
                                </div>

                                <!-- Form Section -->
                                <div class="form-glass" style="margin-top: 30px;">
                                    <form id="form-orange" action="kube" method="post" enctype="multipart/form-data"
                                          onsubmit="addUUID(this); revealText(this, 'orange','');">
                                        <input type="hidden" id="call" name="call" value="gcp">

                                        <div class="form-group" style="margin-bottom: 25px;">
                                            <label class="text-glass" style="margin-bottom: 8px;">Project ID</label>
                                            <input type="text" placeholder="Enter Project ID" id="projid" name="projid" class="input-glass" required>
                                        </div>

                                        <button id="gcpc" type="submit" class="btn-gradient">Generate</button>
                                    </form>

                                    <div id="text-block-container-orange" style="filter:none"></div>
                                </div>
                            </div>
                        </div>

                        <!-- EKS Inventory Tab -->
                        <div id="eks" class="tab-pane fade">
                            <div class="card-glass animate-slide-in">
                                <h3 class="text-glass" style="margin-bottom: 20px;">
                                    Generate EKS Inventory Report
                                    <span style="color: #F08000"> by entering the AWS Region</span>
                                </h3>

                                <!-- Info Tabs -->
                                <div class="info-tabs" style="margin-bottom: 30px;">
                                    <button class="btn-glass" onclick="openCity(event, 'eks_req')" style="margin-right: 10px;">Requirements</button>
                                    <button class="btn-glass" onclick="openCity(event, 'eks_steps')" style="margin-right: 10px;">Steps</button>
                                    <button class="btn-glass" onclick="openCity(event, 'eks_out')" style="margin-right: 10px;">Output</button>
                                    <button class="btn-glass" onclick="openCity(event, 'eks_vid')" style="margin-right: 10px;">Video</button>
                                    <button class="btn-glass" onclick="openCity(event, 'eks_wn')">What Next?</button>
                                </div>

                                <!-- Info Content -->
                                <div id="eks_req" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">
                                        1. Cloud Run Google Service Account <span style="color: #4facfe"><EMAIL></span>
                                        requires AWS managed IAM Policy <span class="text-glass" style="font-weight: bold">arn:aws:iam::aws:policy/ReadOnlyAccess</span> on AWS project.
                                    </p>
                                </div>

                                <div id="eks_steps" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">
                                        1. Download <a href="https://storage.googleapis.com/hypatia-public-artifacts/hypatia-aws-eks-cfn.yaml"
                                           target="_blank" class="text-glass" style="color: #4facfe;">AWS Cloudformation file</a> and use the
                                        <a href="https://docs.google.com/document/d/1NtyMtZHWPc5eaZn5fcXBqYEHDCSyd87NrDFHgsAT6GY/view"
                                           target="_blank" class="text-glass" style="color: #4facfe;">instructions</a>.<br>
                                        2. The Stack should create a IAM Role and will output its ARN.<br>
                                        3. You need to supply the ARNs and Region in the form.<br>
                                        4. Click Generate.
                                    </p>
                                </div>

                                <div id="eks_out" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <a href="#" target="_blank" class="btn-gradient" style="margin-bottom: 20px;">Sample Output</a>
                                    <div style="text-align: center;">
                                        <img src="./static/assets/images/eksinvent.png" style="max-width: 100%; border-radius: 12px;" />
                                    </div>
                                </div>

                                <div id="eks_vid" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">Coming soon...</p>
                                </div>

                                <div id="eks_wn" class="info-content glass-subtle" style="display: none; padding: 20px; border-radius: 12px; margin-bottom: 20px;">
                                    <p class="text-glass-secondary">No further action is required.</p>
                                </div>

                                <!-- Form Section -->
                                <div class="form-glass" style="margin-top: 30px;">
                                    <form id="form-eks" action="kube" method="post" enctype="multipart/form-data"
                                          onsubmit="addUUID(this); revealText(this, 'eks','');">
                                        <input type="hidden" id="call" name="call" value="aws">

                                        <div class="form-group" style="margin-bottom: 25px;">
                                            <label class="text-glass" style="margin-bottom: 8px;">ARN</label>
                                            <input type="text" placeholder="Enter ARN" id="eks_arn" name="arn" class="input-glass" required>
                                        </div>

                                        <div class="form-group" style="margin-bottom: 25px;">
                                            <label class="text-glass" style="margin-bottom: 8px;">Region</label>
                                            <input type="text" placeholder="us-east-1" id="eks_region" name="region" class="input-glass" required>
                                        </div>

                                        <button id="eksin" type="submit" class="btn-gradient">Generate</button>
                                    </form>

                                    <div id="text-block-container-eks" style="filter:none"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="div2" class="hidden">
                    <h2 class="text-glass" style="text-align: center; margin-bottom: 30px;">Compliance Monitoring</h2>
                    <!-- Compliance content will be added -->
                </div>
                
                <div id="div3" class="hidden">
                    <h2 class="text-glass" style="text-align: center; margin-bottom: 30px;">Assessment Tools</h2>
                    <!-- Assessment content will be added -->
                </div>
                
                <div id="div4" class="hidden">
                    <h2 class="text-glass" style="text-align: center; margin-bottom: 30px;">Migration Planning</h2>
                    <!-- Migration content will be added -->
                </div>
                
                <div id="div5" class="hidden">
                    <h2 class="text-glass" style="text-align: center; margin-bottom: 30px;">Glide Automation</h2>
                    <!-- Glide content will be added -->
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="./static/scripts/main.js"></script>
    <script>
        // Enhanced scroll effect for navbar
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navi');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Enhanced tab switching with animations
        function switchCont(invoker) {
            // Remove active class from all buttons
            for (var i = 1; i <= 5; i++) {
                var div = document.getElementById('div' + i);
                var button = document.getElementById('buto' + i);

                if (i === invoker) {
                    // Fade in the selected section
                    div.style.display = 'block';
                    div.style.animation = 'slideInUp 0.6s ease-out';
                    button.classList.add('active');
                } else {
                    div.style.display = 'none';
                    button.classList.remove('active');
                }
            }
        }

        // Info tab functionality
        function openCity(evt, cityName) {
            var i, tabcontent, tablinks;

            // Check if the clicked tab is already active
            if (document.getElementById(cityName).style.display === "block") {
                document.getElementById(cityName).style.display = "none";
                evt.currentTarget.classList.remove("active");
                return;
            }

            // Hide all info content in the same parent
            var parent = evt.currentTarget.closest('.card-glass');
            var tabcontent = parent.getElementsByClassName("info-content");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }

            // Remove active class from all buttons in the same parent
            var tablinks = parent.getElementsByClassName("btn-glass");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].classList.remove("active");
            }

            // Show the selected content and mark button as active
            document.getElementById(cityName).style.display = "block";
            document.getElementById(cityName).style.animation = "slideIn 0.4s ease-out";
            evt.currentTarget.classList.add("active");
        }

        // Enhanced Bootstrap tab functionality for inventory tabs
        $(document).ready(function() {
            $('.nav-tabs a').click(function(e) {
                e.preventDefault();

                // Remove active class from all tabs
                $('.nav-tabs a').removeClass('active');
                $('.tab-pane').removeClass('active show');

                // Add active class to clicked tab
                $(this).addClass('active');

                // Show corresponding content with animation
                var target = $(this).attr('href');
                $(target).addClass('active show');
                $(target).css('animation', 'slideInUp 0.6s ease-out');
            });
        });

        // Form validation and enhancement
        function checkFields() {
            // Add form validation logic here
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                const inputs = form.querySelectorAll('input[required]');
                const submitBtn = form.querySelector('button[type="submit"]');

                function validateForm() {
                    let allValid = true;
                    inputs.forEach(input => {
                        if (!input.value.trim()) {
                            allValid = false;
                        }
                    });

                    if (submitBtn) {
                        submitBtn.disabled = !allValid;
                        if (allValid) {
                            submitBtn.classList.remove('disabled');
                        } else {
                            submitBtn.classList.add('disabled');
                        }
                    }
                }

                inputs.forEach(input => {
                    input.addEventListener('input', validateForm);
                    input.addEventListener('change', validateForm);
                });

                validateForm(); // Initial check
            });
        }

        // Initialize form validation when page loads
        document.addEventListener('DOMContentLoaded', function() {
            checkFields();
        });

        // Original functions from main.js for compatibility
        const uuidHex = crypto.randomUUID().replace(/-/g, '');

        function addUUID(form) {
            const uuid = crypto.randomUUID().replace(/-/g, '');
            let uuidInput = form.elements["uuid"];
            if (!uuidInput) {
                uuidInput = document.createElement("input");
                uuidInput.type = "hidden";
                uuidInput.name = "uuid";
                uuidInput.id = "uuid-input";
                form.appendChild(uuidInput);
            }
            uuidInput.value = uuid;
        }

        function revealText(form, col, plat) {
            const uuid = crypto.randomUUID().replace(/-/g, '');
            let uuidInput = form.elements["uuid"];
            if (!uuidInput) {
                uuidInput = document.createElement("input");
                uuidInput.type = "hidden";
                uuidInput.name = "uuid";
                uuidInput.id = "uuid-input";
                form.appendChild(uuidInput);
            }
            uuidInput.value = uuid;

            // Remove any existing overlay
            let existingOverlay = document.getElementById("loadingOverlay");
            if (existingOverlay) {
                existingOverlay.remove();
            }

            // Create Loading Overlay with glass effect
            let overlay = document.createElement("div");
            overlay.id = "loadingOverlay";
            overlay.className = "modal-overlay";
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.3);
                backdrop-filter: blur(15px);
                -webkit-backdrop-filter: blur(15px);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            `;

            // Create Loading Box
            let loadingBox = document.createElement("div");
            loadingBox.className = "loading-glass";
            loadingBox.style.cssText = `
                background: rgba(255, 255, 255, 0.15);
                backdrop-filter: blur(25px);
                -webkit-backdrop-filter: blur(25px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 24px;
                padding: 40px;
                text-align: center;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
                max-width: 400px;
                width: 90%;
            `;

            // Close Button
            let closeButton = document.createElement("button");
            closeButton.innerHTML = "×";
            closeButton.style.cssText = `
                position: absolute;
                top: 15px;
                right: 20px;
                background: none;
                border: none;
                color: white;
                font-size: 24px;
                cursor: pointer;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.3s ease;
            `;
            closeButton.onmouseover = function() {
                this.style.background = 'rgba(255, 255, 255, 0.2)';
            };
            closeButton.onmouseout = function() {
                this.style.background = 'none';
            };
            closeButton.onclick = function () {
                overlay.remove();
            };

            // Title
            let title = document.createElement("h5");
            title.className = "text-glass";
            title.innerText = "Processing Request...";
            title.style.marginBottom = "20px";

            // Spinner
            let spinner = document.createElement("div");
            spinner.className = "spinner-glass";
            spinner.style.cssText = `
                width: 40px;
                height: 40px;
                border: 3px solid rgba(255, 255, 255, 0.2);
                border-top: 3px solid white;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
            `;

            // Timer
            let timer = document.createElement("p");
            timer.id = "timer";
            timer.className = "text-glass-secondary";
            timer.innerHTML = "<strong>Elapsed Time: 00:00</strong>";

            // Append Elements
            loadingBox.style.position = "relative";
            loadingBox.appendChild(closeButton);
            loadingBox.appendChild(title);
            loadingBox.appendChild(spinner);
            loadingBox.appendChild(timer);
            overlay.appendChild(loadingBox);
            document.body.appendChild(overlay);

            // Start timer
            let startTime = Date.now();
            let timerInterval = setInterval(function() {
                let elapsed = Math.floor((Date.now() - startTime) / 1000);
                let minutes = Math.floor(elapsed / 60);
                let seconds = elapsed % 60;
                timer.innerHTML = `<strong>Elapsed Time: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}</strong>`;
            }, 1000);

            // Store timer reference for cleanup
            closeButton.onclick = function () {
                clearInterval(timerInterval);
                overlay.remove();
            };
        }
    </script>
</body>
</html>
