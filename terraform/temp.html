<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NebulaFlow - Migration Planning</title>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Open+Sans&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #f5f7fa;
            color: #333333;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            width: 250px;
            background-color: #003366;
            color: white;
            height: 100vh;
            position: fixed;
            padding: 20px;
        }

        .sidebar h2 {
            font-family: 'Montserrat', sans-serif;
        }

        .sidebar ul {
            list-style-type: none;
            padding: 0;
        }

        .sidebar ul li {
            margin: 10px 0;
        }

        .sidebar ul li a {
            color: white;
            text-decoration: none;
        }

        .main-content {
            margin-left: 270px;
            padding: 20px;
        }

        .category-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .category-card h2 {
            font-family: 'Montserrat', sans-serif;
            color: #003366;
        }

        .explore-button {
            background-color: #9b59b6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }

        .subcategory-nav {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }

        .subcategory-nav ul {
            display: flex;
            list-style-type: none;
            padding: 0;
        }

        .subcategory-nav ul li {
            margin: 0 10px;
        }

        .subcategory-nav ul li a {
            text-decoration: none;
            color: #3498db;
        }

        .migration-form {
            display: flex;
            flex-direction: column;
            max-width: 500px;
        }

        .migration-form label {
            margin-top: 10px;
        }

        .migration-form input, .migration-form textarea {
            padding: 10px;
            margin-top: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .submit-button {
            background-color: #9b59b6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <h2>NebulaFlow</h2>
        <ul>
            <li><a href="#">Migration Planning</a></li>
            <li><a href="#">Automation Tools</a></li>
            <li><a href="#">Resource Management</a></li>
            <li><a href="#">Security & Compliance</a></li>
            <li><a href="#">Support & Training</a></li>
        </ul>
    </div>

    <div class="main-content">
        <div class="category-card">
            <h2>Migration Planning</h2>
            <p>Plan your cloud migration strategy.</p>
            <button class="explore-button">Explore</button>
        </div>

        <div class="subcategory-nav">
            <ul>
                <li><a href="#requirements">Requirements</a></li>
                <li><a href="#steps">Steps</a></li>
                <li><a href="#sample-output">Sample Output</a></li>
                <li><a href="#video">Video</a></li>
                <li><a href="#next-step">Next Step</a></li>
            </ul>
        </div>

        <div class="migration-form">
            <h3>Migration Form</h3>
            <form>
                <label for="project-name">Project Name:</label>
                <input type="text" id="project-name" name="project-name">

                <label for="description">Description:</label>
                <textarea id="description" name="description"></textarea>

                <button type="submit" class="submit-button">Submit</button>
            </form>
        </div>
    </div>
</body>
</html>
