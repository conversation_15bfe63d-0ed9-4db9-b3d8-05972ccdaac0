<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HypatIa Cloud Automation Platform</title>
    <link rel="icon" sizes="180x180" href="./static/assets/images/hypatia.png" />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    
    <!-- Custom Liquid Glass CSS -->
    <link rel="stylesheet" href="./static/styles/liquid-glass.css" />
    
    <style>
        @font-face {
            font-family: 'HeilHydra';
            src: url('./static/assets/fonts/hydra.ttf') format('truetype');
        }

        /* Black & White Dashboard Layout */
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: #ffffff;
            position: relative;
            overflow-x: hidden;
        }

        .dashboard-container {
            display: grid;
            grid-template-areas: 
                "sidebar header"
                "sidebar main";
            grid-template-columns: 280px 1fr;
            grid-template-rows: 80px 1fr;
            min-height: 100vh;
        }

        /* Sidebar - Pure Black */
        .sidebar {
            grid-area: sidebar;
            background: #000000;
            border-right: 1px solid #333333;
            padding: 20px;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            margin-bottom: 40px;
            padding: 20px 0;
            border-bottom: 1px solid #333333;
        }

        .sidebar-logo img {
            width: 32px;
            height: 32px;
            margin-right: 12px;
        }

        .sidebar-logo .brand-text {
            font-family: 'HeilHydra', sans-serif;
            font-size: 24px;
            color: #ffffff;
        }

        .sidebar-logo .beta-text {
            font-size: 8px;
            vertical-align: super;
            color: #06b6d4;
        }

        .sidebar-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-nav li {
            margin-bottom: 8px;
        }

        .sidebar-nav a {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: #cccccc;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            transform: translateX(4px);
        }

        .sidebar-nav a.active {
            background: rgba(255, 255, 255, 0.15);
            border-left: 3px solid #3b82f6;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            opacity: 0.7;
        }

        /* Header - Pure White */
        .header {
            grid-area: header;
            background: #ffffff;
            border-bottom: 1px solid #e5e5e5;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #000000;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #000000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        /* Main Content - Pure White */
        .main-content {
            grid-area: main;
            padding: 30px;
            overflow-y: auto;
            background: #ffffff;
        }

        /* Section Content */
        .section-content {
            display: none;
        }

        .section-content.active {
            display: block;
            animation: fadeInUp 0.6s ease-out;
        }

        /* Glass Cards for Content */
        .content-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .content-card:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(0, 0, 0, 0.2);
            transform: translateY(-4px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .content-card h3 {
            color: #000000;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .content-card p {
            color: #666666;
            line-height: 1.6;
        }

        /* Form Styles - Compact for viewport fit */
        .form-glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            margin-bottom: 6px;
            font-size: 14px;
        }

        .input-glass {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            color: #000000;
            padding: 12px 16px;
            width: 100%;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .input-glass::placeholder {
            color: #999999;
        }

        .input-glass:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.1);
            border-color: #3b82f6;
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
        }

        /* Button Styles */
        .btn-glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            color: #000000;
            padding: 12px 24px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-glass:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(0, 0, 0, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            color: #000000;
            text-decoration: none;
        }

        .btn-gradient {
            background: linear-gradient(135deg, #000000 0%, #333333 100%);
            border: none;
            border-radius: 12px;
            color: #ffffff;
            padding: 12px 24px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
        }

        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
        }

        /* Tab Styles */
        .tab-glass {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 12px 24px;
            color: #666666;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .tab-glass:hover,
        .tab-glass.active {
            background: rgba(255, 255, 255, 0.1);
            color: #000000;
            transform: translateY(-1px);
            text-decoration: none;
        }

        /* Info Content */
        .info-content {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            display: none;
        }

        .info-content.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        .info-content p {
            color: #666666;
            margin: 0;
            line-height: 1.6;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Chatbot Styles */
        .chatbot-container {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 10000;
        }

        .chatbot-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chatbot-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .chatbot-button span {
            font-size: 24px;
        }

        .chatbot-window {
            position: absolute;
            bottom: 80px;
            right: 0;
            width: 350px;
            height: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            display: none;
            flex-direction: column;
            overflow: hidden;
        }

        .chatbot-window.open {
            display: flex;
            animation: slideInUp 0.3s ease-out;
        }

        .chatbot-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .chatbot-header h4 {
            margin: 0;
            color: #000000;
            font-size: 16px;
            font-weight: 600;
        }

        .chatbot-close {
            background: none;
            border: none;
            color: #666666;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .chatbot-close:hover {
            background: rgba(0, 0, 0, 0.1);
            color: #000000;
        }

        .chatbot-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 16px;
            font-size: 14px;
            line-height: 1.4;
        }

        .message.user {
            align-self: flex-end;
            background: rgba(0, 0, 0, 0.8);
            color: #ffffff;
        }

        .message.bot {
            align-self: flex-start;
            background: rgba(255, 255, 255, 0.1);
            color: #000000;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .chatbot-input-container {
            padding: 20px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 12px;
        }

        .chatbot-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 12px;
            padding: 12px 16px;
            color: #000000;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .chatbot-input:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.1);
            border-color: #3b82f6;
        }

        .chatbot-send {
            background: rgba(0, 0, 0, 0.8);
            border: none;
            border-radius: 12px;
            color: #ffffff;
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .chatbot-send:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: translateY(-1px);
        }

        .chatbot-send:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: flex;
            gap: 4px;
            padding: 12px 16px;
            align-self: flex-start;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #666666;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.5;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .chatbot-container {
                bottom: 20px;
                right: 20px;
            }

            .chatbot-window {
                width: 300px;
                height: 400px;
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-areas: 
                    "header"
                    "main";
                grid-template-columns: 1fr;
                grid-template-rows: 80px 1fr;
            }
            
            .sidebar {
                display: none;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-logo">
                <img src="./static/assets/images/hypatia.png" alt="HypatIa">
                <span class="brand-text">HypatIa<sup class="beta-text">BETA</sup></span>
            </div>

            <nav>
                <ul class="sidebar-nav">
                    <li><a href="#" class="nav-link active" data-section="inventory">
                        <span class="nav-icon">📦</span>
                        Inventory
                    </a></li>
                    <li><a href="#" class="nav-link" data-section="compliance">
                        <span class="nav-icon">🛡️</span>
                        Compliance
                    </a></li>
                    <li><a href="#" class="nav-link" data-section="assessment">
                        <span class="nav-icon">📋</span>
                        Assessment
                    </a></li>
                    <li><a href="#" class="nav-link" data-section="migration">
                        <span class="nav-icon">🚀</span>
                        Migration
                    </a></li>
                    <li><a href="#" class="nav-link" data-section="glide">
                        <span class="nav-icon">⚡</span>
                        Glide
                    </a></li>
                </ul>
            </nav>
        </aside>

        <!-- Header -->
        <header class="header">
            <h1 class="header-title" id="page-title">Cloud Inventory Management</h1>
            <div class="header-actions">
                <div class="user-profile">
                    <div class="user-avatar">U</div>
                    <span style="color: #666666;">Admin User</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <div id="astirth" class="tab-content">
                <!-- Inventory Section (div1) -->
                <div id="div1" class="tab-pane fade in active show">
                    <div class="nav-container">
                        <ul class="nav nav-tabs" style="border: none; justify-content: center; margin-bottom: 40px;">
                            <li style="margin: 0 10px;">
                                <a class="tab-glass active" data-toggle="tab" href="#software">
                                    <img src="./static/assets/images/azinv1.png" style="width: 24px; height: 24px; margin-right: 8px;" />
                                    <span>GCP Inventory</span>
                                </a>
                            </li>
                            <li style="margin: 0 10px;">
                                <a class="tab-glass" data-toggle="tab" href="#aws1">
                                    <img src="./static/assets/images/awsinv1.png" style="width: 24px; height: 24px; margin-right: 8px;" />
                                    <span>AWS Inventory</span>
                                </a>
                            </li>
                            <li style="margin: 0 10px;">
                                <a class="tab-glass" data-toggle="tab" href="#ari">
                                    <img src="./static/assets/images/inv1.png" style="width: 24px; height: 24px; margin-right: 8px;" />
                                    <span>Azure Inventory</span>
                                </a>
                            </li>
                            <li style="margin: 0 10px;">
                                <a class="tab-glass" data-toggle="tab" href="#oci_inv">
                                    <img src="./static/assets/images/oci_inv1.png" style="width: 24px; height: 24px; margin-right: 8px;" />
                                    <span>Oracle Inventory</span>
                                </a>
                            </li>
                            <li style="margin: 0 10px;">
                                <a class="tab-glass" data-toggle="tab" href="#gke_dragon">
                                    <img src="./static/assets/images/google-gke.svg" style="width: 24px; height: 24px; margin-right: 8px;" />
                                    <span>GKE Inventory</span>
                                </a>
                            </li>
                            <li style="margin: 0 10px;">
                                <a class="tab-glass" data-toggle="tab" href="#eks">
                                    <img src="./static/assets/images/eksinv.png" style="width: 24px; height: 24px; margin-right: 8px;" />
                                    <span>EKS Inventory</span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <div class="tab-content">
                        <!-- GCP Inventory Tab -->
                        <div id="gcp-inventory" class="tab-pane fade in active show">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    Generate GCP Inventory Workbook for an organisation
                                    <span style="color: #06b6d4"> by giving org id</span>
                                </h4>

                                <!-- Info Tabs -->
                                <div style="margin-bottom: 30px;">
                                    <button class="btn-glass" onclick="openInfoTab(event, 'gcp_req')" style="margin-right: 10px;">Requirements</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'gcp_steps')" style="margin-right: 10px;">Steps</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'gcp_out')" style="margin-right: 10px;">Output</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'gcp_vid')" style="margin-right: 10px;">Video</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'gcp_wn')">What Next?</button>
                                </div>

                                <!-- Info Content -->
                                <div id="gcp_req" class="info-content" style="display: none;">
                                    <p><strong>Requirements:</strong><br>
                                    • Browser and Viewer access at Organization Level for Cloud Run Service Account<br>
                                    • Viewer access at Project level when projects have been selected<br>
                                    • Valid GCP Organization ID or Project IDs</p>
                                </div>

                                <div id="gcp_steps" class="info-content" style="display: none;">
                                    <p><strong>Steps:</strong><br>
                                    1. Fill in at least one field based on your requirements<br>
                                    2. Enter GCP Organization ID to create an inventory for the whole organization<br>
                                    3. Enter Folder IDs (comma-separated) to create an inventory for specific folders<br>
                                    4. Enter Project IDs (comma-separated) to create an inventory for specific projects<br>
                                    5. To include Redis, provide comma separated regions. Default region is us-central1</p>
                                </div>

                                <div id="gcp_out" class="info-content" style="display: none;">
                                    <p><strong>Output:</strong><br>
                                    • Comprehensive Excel workbook with detailed resource inventory<br>
                                    • Service-wise breakdown including Compute, Storage, Networking<br>
                                    • Cost analysis and resource utilization metrics<br>
                                    • Security and compliance status overview</p>
                                    <a href="https://docs.google.com/spreadsheets/d/1_D9pkJiBefJ1MXF9KGN97sz_h1qmYFT-/view"
                                       target="_blank" class="btn-gradient" style="margin-top: 15px;">View Sample Output</a>
                                </div>

                                <div id="gcp_vid" class="info-content" style="display: none;">
                                    <iframe src="https://drive.google.com/file/d/1NZ96Cgwku385dW0A8MSAssljRudJH3n3/preview"
                                            width="100%" height="420" allow="autoplay; fullscreen;" style="border-radius: 12px;"></iframe>
                                </div>

                                <div id="gcp_wn" class="info-content" style="display: none;">
                                    <p>No further action is required after the inventory generation is complete.</p>
                                </div>

                                <!-- Form Section -->
                                <form id="gcp-form" action="inventory" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'gcp','');" style="margin-top: 30px;">
                                    <input type="hidden" name="call" value="gcp">

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Organization ID</label>
                                        <input type="text" placeholder="Enter Organization ID" name="orgid" class="input-glass">
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Folder IDs</label>
                                        <input type="text" placeholder="folder1,folder2,folder3" name="folderids" class="input-glass">
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Project IDs</label>
                                        <input type="text" placeholder="project1,project2,project3" name="projids" class="input-glass">
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Redis/Memcache Regions</label>
                                        <input type="text" placeholder="us-central1,us-east1" name="redis-reg" class="input-glass">
                                    </div>

                                    <button type="submit" class="btn-gradient">Generate Inventory</button>
                                </form>

                                <div id="text-block-container-gcp" style="filter:none; margin-top: 20px;"></div>
                            </div>
                        </div>

                        <!-- AWS Inventory Tab -->
                        <div id="aws-inventory" class="tab-pane fade">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    Generate AWS Inventory Workbook for an organisation
                                    <span style="color: #06b6d4"> by providing ARN</span>
                                </h4>

                                <!-- Info Tabs -->
                                <div style="margin-bottom: 30px;">
                                    <button class="btn-glass" onclick="openInfoTab(event, 'aws_req')" style="margin-right: 10px;">Requirements</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'aws_steps')" style="margin-right: 10px;">Steps</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'aws_out')" style="margin-right: 10px;">Output</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'aws_vid')" style="margin-right: 10px;">Video</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'aws_wn')">What Next?</button>
                                </div>

                                <!-- Info Content -->
                                <div id="aws_req" class="info-content" style="display: none;">
                                    <p><strong>Requirements:</strong><br>
                                    • Cloud Run Google Service Account requires AWS IAM Policy ReadOnlyAccess<br>
                                    • Valid AWS ARN with appropriate permissions<br>
                                    • Access to AWS CloudFormation for role creation</p>
                                </div>

                                <div id="aws_steps" class="info-content" style="display: none;">
                                    <p><strong>Steps:</strong><br>
                                    1. Download AWS CloudFormation file and follow setup instructions<br>
                                    2. The Stack will create an IAM Role and output its ARN<br>
                                    3. Supply the ARN in the form below<br>
                                    4. Optionally provide comma-separated regions<br>
                                    5. Click Generate to start the inventory process</p>
                                    <a href="https://storage.googleapis.com/hypatia-public-artifacts/hypatia-aws-eks-cfn.yaml"
                                       target="_blank" class="btn-gradient" style="margin-top: 15px;">Download CloudFormation</a>
                                </div>

                                <div id="aws_out" class="info-content" style="display: none;">
                                    <p><strong>Output:</strong><br>
                                    • Comprehensive AWS resource inventory across all services<br>
                                    • Regional breakdown of resources and costs<br>
                                    • Security group and IAM policy analysis<br>
                                    • Cost optimization recommendations</p>
                                    <a href="https://docs.google.com/spreadsheets/d/1tGel4HcGTm5KTpiTSB5Ain4GDZP3687dGzwff_WP1C0/view"
                                       target="_blank" class="btn-gradient" style="margin-top: 15px;">View Sample Output</a>
                                </div>

                                <div id="aws_vid" class="info-content" style="display: none;">
                                    <iframe src="https://drive.google.com/file/d/12PsnDsvWwl8ArrsXqgoH6wul35J78DHi/preview"
                                            width="100%" height="420" allow="autoplay; fullscreen;" style="border-radius: 12px;"></iframe>
                                </div>

                                <div id="aws_wn" class="info-content" style="display: none;">
                                    <p>No further action is required after the inventory generation is complete.</p>
                                </div>

                                <!-- Form Section -->
                                <form id="aws-form" action="inventory" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'aws','');" style="margin-top: 30px;">
                                    <input type="hidden" name="call" value="aws">

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">ARN</label>
                                        <input type="text" placeholder="Enter AWS ARN" name="arn" class="input-glass" required>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Regions</label>
                                        <input type="text" placeholder="us-east-1,us-west-2 (or * for all)" name="regions" class="input-glass">
                                    </div>

                                    <button type="submit" class="btn-gradient">Generate Inventory</button>
                                </form>

                                <div id="text-block-container-aws" style="filter:none; margin-top: 20px;"></div>
                            </div>
                        </div>

                        <!-- Azure Inventory Tab -->
                        <div id="azure-inventory" class="tab-pane fade">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    Generate Azure Inventory Workbook
                                    <span style="color: #0078d4"> by uploading credentials</span>
                                </h4>

                                <!-- Info Tabs -->
                                <div style="margin-bottom: 30px;">
                                    <button class="btn-glass" onclick="openInfoTab(event, 'azure_req')" style="margin-right: 10px;">Requirements</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'azure_steps')" style="margin-right: 10px;">Steps</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'azure_out')" style="margin-right: 10px;">Output</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'azure_wn')">What Next?</button>
                                </div>

                                <!-- Info Content -->
                                <div id="azure_req" class="info-content" style="display: none;">
                                    <p><strong>Requirements:</strong><br>
                                    • Create a Service Principal with Reader access<br>
                                    • Generate credentials file with required permissions<br>
                                    • Upload the credentials file in JSON format</p>
                                </div>

                                <div id="azure_steps" class="info-content" style="display: none;">
                                    <p><strong>Steps:</strong><br>
                                    1. Create Azure Service Principal with Reader permissions<br>
                                    2. Download the credentials file in JSON format<br>
                                    3. Upload the file using the form below<br>
                                    4. Click Generate to start the inventory process</p>
                                </div>

                                <div id="azure_out" class="info-content" style="display: none;">
                                    <p><strong>Output:</strong><br>
                                    • Complete Azure resource inventory across all subscriptions<br>
                                    • Resource group and service categorization<br>
                                    • Cost analysis and optimization recommendations<br>
                                    • Security and compliance assessment</p>
                                </div>

                                <div id="azure_wn" class="info-content" style="display: none;">
                                    <p>Remove the service principal created to fetch the inventory for security purposes.</p>
                                </div>

                                <!-- Form Section -->
                                <form id="azure-form" action="inventory" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'azure','');" style="margin-top: 30px;">
                                    <input type="hidden" name="call" value="azure">

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Azure Credentials File</label>
                                        <input type="file" name="azure_creds" accept=".json" class="input-glass" required>
                                    </div>

                                    <button type="submit" class="btn-gradient">Generate Inventory</button>
                                </form>

                                <div id="text-block-container-azure" style="filter:none; margin-top: 20px;"></div>
                            </div>
                        </div>

                        <!-- Oracle Inventory Tab -->
                        <div id="oracle-inventory" class="tab-pane fade">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    Generate Oracle Cloud Inventory
                                    <span style="color: #f5c518"> by uploading OCI credentials</span>
                                </h4>

                                <!-- Info Tabs -->
                                <div style="margin-bottom: 30px;">
                                    <button class="btn-glass" onclick="openInfoTab(event, 'oci_req')" style="margin-right: 10px;">Requirements</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'oci_steps')" style="margin-right: 10px;">Steps</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'oci_out')" style="margin-right: 10px;">Output</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'oci_wn')">What Next?</button>
                                </div>

                                <!-- Info Content -->
                                <div id="oci_req" class="info-content" style="display: none;">
                                    <p><strong>Requirements:</strong><br>
                                    • Create a User with Reader Access in OCI<br>
                                    • Generate user credentials export file<br>
                                    • Specify Region for the user (inventory is generated per region)</p>
                                </div>

                                <div id="oci_steps" class="info-content" style="display: none;">
                                    <p><strong>Steps:</strong><br>
                                    1. Follow the OCI credential setup instructions<br>
                                    2. Upload the credential zip file<br>
                                    3. Provide the target Region<br>
                                    4. Click Generate to start the inventory process</p>
                                    <a href="https://docs.google.com/document/d/1XcAl64pVcYvU9chOKs76nmzW-3K2-EIvFRaunoNF_as/view"
                                       target="_blank" class="btn-gradient" style="margin-top: 15px;">View Instructions</a>
                                </div>

                                <div id="oci_out" class="info-content" style="display: none;">
                                    <p><strong>Output:</strong><br>
                                    • Regional Oracle Cloud resource inventory<br>
                                    • Compute, storage, and networking resource details<br>
                                    • Cost analysis and utilization metrics<br>
                                    • Security configuration assessment</p>
                                    <a href="https://docs.google.com/spreadsheets/d/16jy624Of6p0qOlhCFNCIDwp8IMRp1N2nuTEcYVqqBoI/view"
                                       target="_blank" class="btn-gradient" style="margin-top: 15px;">View Sample Output</a>
                                </div>

                                <div id="oci_wn" class="info-content" style="display: none;">
                                    <p>Remove the user created to fetch the inventory for security purposes.</p>
                                </div>

                                <!-- Form Section -->
                                <form id="oci-form" action="inventory" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'oci','');" style="margin-top: 30px;">
                                    <input type="hidden" name="call" value="oci">

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">OCI Credentials Zip File</label>
                                        <input type="file" name="oci_zip_file" accept=".zip" class="input-glass" required>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">OCI Region</label>
                                        <input type="text" placeholder="ap-mumbai-1" name="region" value="ap-mumbai-1" class="input-glass" required>
                                    </div>

                                    <button type="submit" class="btn-gradient">Generate Inventory</button>
                                </form>

                                <div id="text-block-container-oci" style="filter:none; margin-top: 20px;"></div>
                            </div>
                        </div>

                        <!-- GKE Inventory Tab -->
                        <div id="gke-inventory" class="tab-pane fade">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    Generate GKE Inventory Sheet for all clusters in project
                                    <span style="color: #F08000"> by giving project id</span>
                                </h4>

                                <!-- Info Tabs -->
                                <div style="margin-bottom: 30px;">
                                    <button class="btn-glass" onclick="openInfoTab(event, 'gke_req')" style="margin-right: 10px;">Requirements</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'gke_steps')" style="margin-right: 10px;">Steps</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'gke_out')" style="margin-right: 10px;">Output</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'gke_wn')">What Next?</button>
                                </div>

                                <!-- Info Content -->
                                <div id="gke_req" class="info-content" style="display: none;">
                                    <p><strong>Requirements:</strong><br>
                                    • Cluster Endpoint must be public for the probe to be successful<br>
                                    • Cloud Run Service Account needs Kubernetes Engine Viewer IAM Role<br>
                                    • Valid GCP Project ID with GKE clusters</p>
                                </div>

                                <div id="gke_steps" class="info-content" style="display: none;">
                                    <p><strong>Steps:</strong><br>
                                    1. Enter the Project ID where GKE clusters are present<br>
                                    2. Ensure clusters have public endpoints<br>
                                    3. Click Generate to start the inventory process</p>
                                </div>

                                <div id="gke_out" class="info-content" style="display: none;">
                                    <p><strong>Output:</strong><br>
                                    • Comprehensive GKE cluster inventory<br>
                                    • Node pool and workload analysis<br>
                                    • Resource utilization and capacity planning<br>
                                    • Security and configuration assessment</p>
                                    <a href="https://docs.google.com/spreadsheets/d/12khW7WBOeQfYtLIQ7XkTfVpK-39RunbcvY6RkgsU1k0/view"
                                       target="_blank" class="btn-gradient" style="margin-top: 15px;">View Sample Output</a>
                                </div>

                                <div id="gke_wn" class="info-content" style="display: none;">
                                    <p>No further action is required after the inventory generation is complete.</p>
                                </div>

                                <!-- Form Section -->
                                <form id="gke-form" action="kube" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'gke','');" style="margin-top: 30px;">
                                    <input type="hidden" name="call" value="gcp">

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Project ID</label>
                                        <input type="text" placeholder="Enter GCP Project ID" name="projid" class="input-glass" required>
                                    </div>

                                    <button type="submit" class="btn-gradient">Generate Inventory</button>
                                </form>

                                <div id="text-block-container-gke" style="filter:none; margin-top: 20px;"></div>
                            </div>
                        </div>

                        <!-- EKS Inventory Tab -->
                        <div id="eks-inventory" class="tab-pane fade">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    Generate EKS Inventory Report
                                    <span style="color: #F08000"> by entering the AWS Region</span>
                                </h4>

                                <!-- Info Tabs -->
                                <div style="margin-bottom: 30px;">
                                    <button class="btn-glass" onclick="openInfoTab(event, 'eks_req')" style="margin-right: 10px;">Requirements</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'eks_steps')" style="margin-right: 10px;">Steps</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'eks_out')" style="margin-right: 10px;">Output</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'eks_wn')">What Next?</button>
                                </div>

                                <!-- Info Content -->
                                <div id="eks_req" class="info-content" style="display: none;">
                                    <p><strong>Requirements:</strong><br>
                                    • Cloud Run Google Service Account requires AWS IAM Policy ReadOnlyAccess<br>
                                    • Valid AWS ARN with EKS permissions<br>
                                    • Access to AWS CloudFormation for role creation</p>
                                </div>

                                <div id="eks_steps" class="info-content" style="display: none;">
                                    <p><strong>Steps:</strong><br>
                                    1. Download AWS CloudFormation file and follow setup instructions<br>
                                    2. The Stack will create an IAM Role and output its ARN<br>
                                    3. Supply the ARN and Region in the form below<br>
                                    4. Click Generate to start the inventory process</p>
                                    <a href="https://storage.googleapis.com/hypatia-public-artifacts/hypatia-aws-eks-cfn.yaml"
                                       target="_blank" class="btn-gradient" style="margin-top: 15px;">Download CloudFormation</a>
                                </div>

                                <div id="eks_out" class="info-content" style="display: none;">
                                    <p><strong>Output:</strong><br>
                                    • Complete EKS cluster inventory<br>
                                    • Node group and workload analysis<br>
                                    • Resource utilization and capacity planning<br>
                                    • Security and configuration assessment</p>
                                </div>

                                <div id="eks_wn" class="info-content" style="display: none;">
                                    <p>No further action is required after the inventory generation is complete.</p>
                                </div>

                                <!-- Form Section -->
                                <form id="eks-form" action="kube" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'eks','');" style="margin-top: 30px;">
                                    <input type="hidden" name="call" value="aws">

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">ARN</label>
                                        <input type="text" placeholder="Enter AWS ARN" name="arn" class="input-glass" required>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Region</label>
                                        <input type="text" placeholder="us-east-1" name="region" class="input-glass" required>
                                    </div>

                                    <button type="submit" class="btn-gradient">Generate Inventory</button>
                                </form>

                                <div id="text-block-container-eks" style="filter:none; margin-top: 20px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Compliance Section -->
            <div id="compliance-section" class="section-content">
                <div class="content-card">
                    <h3 style="text-align: center; margin-bottom: 30px;">Compliance Monitoring</h3>
                    <p style="text-align: center; margin-bottom: 40px; color: #666666;">
                        Automated security compliance checking and reporting across cloud platforms
                    </p>

                    <!-- Compliance Tabs -->
                    <div style="text-align: center; margin-bottom: 40px;">
                        <a class="tab-glass active" data-toggle="tab" href="#gcp-compliance">
                            <img src="./static/assets/images/azinv1.png" style="width: 20px; height: 20px; margin-right: 8px;" />
                            GCP Compliance
                        </a>
                        <a class="tab-glass" data-toggle="tab" href="#aws-compliance">
                            <img src="./static/assets/images/awsinv1.png" style="width: 20px; height: 20px; margin-right: 8px;" />
                            AWS Compliance
                        </a>
                        <a class="tab-glass" data-toggle="tab" href="#azure-compliance">
                            <img src="./static/assets/images/inv1.png" style="width: 20px; height: 20px; margin-right: 8px;" />
                            Azure Compliance
                        </a>
                    </div>

                    <div class="tab-content">
                        <!-- GCP Compliance Tab -->
                        <div id="gcp-compliance" class="tab-pane fade in active show">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    GCP Security Compliance Assessment
                                    <span style="color: #06b6d4"> for organization security</span>
                                </h4>

                                <!-- Info Tabs -->
                                <div style="margin-bottom: 30px;">
                                    <button class="btn-glass" onclick="openInfoTab(event, 'gcp_comp_req')" style="margin-right: 10px;">Requirements</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'gcp_comp_steps')" style="margin-right: 10px;">Steps</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'gcp_comp_out')" style="margin-right: 10px;">Output</button>
                                </div>

                                <!-- Info Content -->
                                <div id="gcp_comp_req" class="info-content" style="display: none;">
                                    <p><strong>Requirements:</strong><br>
                                    • Security Reviewer access at Organization Level<br>
                                    • Cloud Asset API enabled<br>
                                    • Security Command Center API enabled<br>
                                    • Valid GCP Organization ID</p>
                                </div>

                                <div id="gcp_comp_steps" class="info-content" style="display: none;">
                                    <p><strong>Steps:</strong><br>
                                    1. Enter your GCP Organization ID<br>
                                    2. Select compliance frameworks to assess<br>
                                    3. Choose specific projects or scan entire organization<br>
                                    4. Click Generate to start the compliance scan</p>
                                </div>

                                <div id="gcp_comp_out" class="info-content" style="display: none;">
                                    <p><strong>Output:</strong><br>
                                    • Comprehensive compliance report with findings<br>
                                    • Security recommendations and remediation steps<br>
                                    • Risk assessment and priority matrix<br>
                                    • Compliance score and trend analysis</p>
                                </div>

                                <!-- Form Section -->
                                <form id="gcp-compliance-form" action="compliance" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'gcp-comp','');" style="margin-top: 30px;">
                                    <input type="hidden" name="call" value="gcp">

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Organization ID</label>
                                        <input type="text" placeholder="Enter GCP Organization ID" name="orgid" class="input-glass" required>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Compliance Framework</label>
                                        <select name="framework" class="input-glass" required>
                                            <option value="">Select Framework</option>
                                            <option value="cis">CIS Benchmarks</option>
                                            <option value="nist">NIST Cybersecurity Framework</option>
                                            <option value="iso27001">ISO 27001</option>
                                            <option value="soc2">SOC 2</option>
                                        </select>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Project IDs (Optional)</label>
                                        <input type="text" placeholder="project1,project2,project3" name="projids" class="input-glass">
                                    </div>

                                    <button type="submit" class="btn-gradient">Generate Compliance Report</button>
                                </form>

                                <div id="text-block-container-gcp-comp" style="filter:none; margin-top: 20px;"></div>
                            </div>
                        </div>

                        <!-- AWS Compliance Tab -->
                        <div id="aws-compliance" class="tab-pane fade">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    AWS Security Compliance Assessment
                                    <span style="color: #06b6d4"> for AWS accounts</span>
                                </h4>

                                <!-- Form Section -->
                                <form id="aws-compliance-form" action="compliance" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'aws-comp','');" style="margin-top: 30px;">
                                    <input type="hidden" name="call" value="aws">

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">AWS ARN</label>
                                        <input type="text" placeholder="Enter AWS ARN" name="arn" class="input-glass" required>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Compliance Framework</label>
                                        <select name="framework" class="input-glass" required>
                                            <option value="">Select Framework</option>
                                            <option value="aws-foundational">AWS Foundational Security Standard</option>
                                            <option value="cis">CIS AWS Foundations Benchmark</option>
                                            <option value="pci-dss">PCI DSS</option>
                                            <option value="hipaa">HIPAA</option>
                                        </select>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Regions</label>
                                        <input type="text" placeholder="us-east-1,us-west-2 (or * for all)" name="regions" class="input-glass">
                                    </div>

                                    <button type="submit" class="btn-gradient">Generate Compliance Report</button>
                                </form>

                                <div id="text-block-container-aws-comp" style="filter:none; margin-top: 20px;"></div>
                            </div>
                        </div>

                        <!-- Azure Compliance Tab -->
                        <div id="azure-compliance" class="tab-pane fade">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    Azure Security Compliance Assessment
                                    <span style="color: #0078d4"> for Azure subscriptions</span>
                                </h4>

                                <!-- Form Section -->
                                <form id="azure-compliance-form" action="compliance" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'azure-comp','');" style="margin-top: 30px;">
                                    <input type="hidden" name="call" value="azure">

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Azure Credentials File</label>
                                        <input type="file" name="azure_creds" accept=".json" class="input-glass" required>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Compliance Framework</label>
                                        <select name="framework" class="input-glass" required>
                                            <option value="">Select Framework</option>
                                            <option value="azure-security">Azure Security Benchmark</option>
                                            <option value="cis">CIS Microsoft Azure Foundations</option>
                                            <option value="nist">NIST SP 800-53</option>
                                            <option value="iso27001">ISO 27001</option>
                                        </select>
                                    </div>

                                    <button type="submit" class="btn-gradient">Generate Compliance Report</button>
                                </form>

                                <div id="text-block-container-azure-comp" style="filter:none; margin-top: 20px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Assessment Section -->
            <div id="assessment-section" class="section-content">
                <div class="content-card">
                    <h3 style="text-align: center; margin-bottom: 30px;">Cloud Assessment Tools</h3>
                    <p style="text-align: center; margin-bottom: 40px; color: #666666;">
                        Comprehensive cloud readiness and migration assessment tools
                    </p>

                    <!-- Assessment Tabs -->
                    <div style="text-align: center; margin-bottom: 40px;">
                        <a class="tab-glass active" data-toggle="tab" href="#migration-assessment">
                            🔍 Migration Assessment
                        </a>
                        <a class="tab-glass" data-toggle="tab" href="#vmware-assessment">
                            🖥️ VMware Assessment
                        </a>
                        <a class="tab-glass" data-toggle="tab" href="#cost-assessment">
                            💰 Cost Assessment
                        </a>
                    </div>

                    <div class="tab-content">
                        <!-- Migration Assessment Tab -->
                        <div id="migration-assessment" class="tab-pane fade in active show">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    Cloud Migration Readiness Assessment
                                    <span style="color: #10b981"> for workload analysis</span>
                                </h4>

                                <!-- Info Tabs -->
                                <div style="margin-bottom: 30px;">
                                    <button class="btn-glass" onclick="openInfoTab(event, 'mig_assess_req')" style="margin-right: 10px;">Requirements</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'mig_assess_steps')" style="margin-right: 10px;">Steps</button>
                                    <button class="btn-glass" onclick="openInfoTab(event, 'mig_assess_out')" style="margin-right: 10px;">Output</button>
                                </div>

                                <!-- Info Content -->
                                <div id="mig_assess_req" class="info-content" style="display: none;">
                                    <p><strong>Requirements:</strong><br>
                                    • Current infrastructure inventory<br>
                                    • Application dependency mapping<br>
                                    • Performance and utilization data<br>
                                    • Business requirements and constraints</p>
                                </div>

                                <div id="mig_assess_steps" class="info-content" style="display: none;">
                                    <p><strong>Steps:</strong><br>
                                    1. Upload infrastructure inventory or connect to existing systems<br>
                                    2. Define assessment scope and criteria<br>
                                    3. Configure migration preferences and constraints<br>
                                    4. Run automated assessment analysis</p>
                                </div>

                                <div id="mig_assess_out" class="info-content" style="display: none;">
                                    <p><strong>Output:</strong><br>
                                    • Migration readiness score for each workload<br>
                                    • Recommended migration strategies (6 R's)<br>
                                    • Risk assessment and mitigation plans<br>
                                    • Timeline and resource requirements</p>
                                </div>

                                <!-- Form Section -->
                                <form id="migration-assessment-form" action="assessment" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'mig-assess','');" style="margin-top: 30px;">
                                    <input type="hidden" name="call" value="migration">

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Assessment Name</label>
                                        <input type="text" placeholder="Enter assessment name" name="assessment_name" class="input-glass" required>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Target Cloud Platform</label>
                                        <select name="target_platform" class="input-glass" required>
                                            <option value="">Select Platform</option>
                                            <option value="gcp">Google Cloud Platform</option>
                                            <option value="aws">Amazon Web Services</option>
                                            <option value="azure">Microsoft Azure</option>
                                            <option value="multi">Multi-Cloud</option>
                                        </select>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Infrastructure Data File</label>
                                        <input type="file" name="infra_data" accept=".csv,.xlsx,.json" class="input-glass">
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Assessment Scope</label>
                                        <select name="scope" class="input-glass" required>
                                            <option value="">Select Scope</option>
                                            <option value="full">Full Infrastructure</option>
                                            <option value="applications">Applications Only</option>
                                            <option value="databases">Databases Only</option>
                                            <option value="custom">Custom Selection</option>
                                        </select>
                                    </div>

                                    <button type="submit" class="btn-gradient">Start Assessment</button>
                                </form>

                                <div id="text-block-container-mig-assess" style="filter:none; margin-top: 20px;"></div>
                            </div>
                        </div>

                        <!-- VMware Assessment Tab -->
                        <div id="vmware-assessment" class="tab-pane fade">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    VMware to Cloud Assessment
                                    <span style="color: #f59e0b"> for virtualized workloads</span>
                                </h4>

                                <!-- Form Section -->
                                <form id="vmware-assessment-form" action="assessment" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'vmware-assess','');" style="margin-top: 30px;">
                                    <input type="hidden" name="call" value="vmware">

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">vCenter Server</label>
                                        <input type="text" placeholder="Enter vCenter server address" name="vcenter_server" class="input-glass" required>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Assessment Period (Days)</label>
                                        <select name="assessment_period" class="input-glass" required>
                                            <option value="">Select Period</option>
                                            <option value="7">7 Days</option>
                                            <option value="14">14 Days</option>
                                            <option value="30">30 Days</option>
                                            <option value="90">90 Days</option>
                                        </select>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Target Cloud</label>
                                        <select name="target_cloud" class="input-glass" required>
                                            <option value="">Select Target</option>
                                            <option value="gcp-gce">GCP Compute Engine</option>
                                            <option value="gcp-gke">GCP Google Kubernetes Engine</option>
                                            <option value="aws-ec2">AWS EC2</option>
                                            <option value="aws-eks">AWS EKS</option>
                                            <option value="azure-vm">Azure Virtual Machines</option>
                                            <option value="azure-aks">Azure Kubernetes Service</option>
                                        </select>
                                    </div>

                                    <button type="submit" class="btn-gradient">Start VMware Assessment</button>
                                </form>

                                <div id="text-block-container-vmware-assess" style="filter:none; margin-top: 20px;"></div>
                            </div>
                        </div>

                        <!-- Cost Assessment Tab -->
                        <div id="cost-assessment" class="tab-pane fade">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    Cloud Cost Assessment
                                    <span style="color: #ef4444"> for financial planning</span>
                                </h4>

                                <!-- Form Section -->
                                <form id="cost-assessment-form" action="assessment" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'cost-assess','');" style="margin-top: 30px;">
                                    <input type="hidden" name="call" value="cost">

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Current Monthly IT Spend</label>
                                        <input type="number" placeholder="Enter current monthly spend (USD)" name="current_spend" class="input-glass" required>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Assessment Type</label>
                                        <select name="assessment_type" class="input-glass" required>
                                            <option value="">Select Type</option>
                                            <option value="tco">Total Cost of Ownership (TCO)</option>
                                            <option value="roi">Return on Investment (ROI)</option>
                                            <option value="optimization">Cost Optimization</option>
                                            <option value="comparison">Multi-Cloud Comparison</option>
                                        </select>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Assessment Timeline</label>
                                        <select name="timeline" class="input-glass" required>
                                            <option value="">Select Timeline</option>
                                            <option value="1">1 Year</option>
                                            <option value="3">3 Years</option>
                                            <option value="5">5 Years</option>
                                        </select>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Infrastructure Data</label>
                                        <input type="file" name="cost_data" accept=".csv,.xlsx" class="input-glass">
                                    </div>

                                    <button type="submit" class="btn-gradient">Generate Cost Assessment</button>
                                </form>

                                <div id="text-block-container-cost-assess" style="filter:none; margin-top: 20px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Migration Section -->
            <div id="migration-section" class="section-content">
                <div class="content-card">
                    <h3 style="text-align: center; margin-bottom: 30px;">Migration Planning</h3>
                    <p style="text-align: center; margin-bottom: 40px; color: #666666;">
                        Comprehensive cloud migration planning and execution tools
                    </p>

                    <!-- Migration Tabs -->
                    <div style="text-align: center; margin-bottom: 40px;">
                        <a class="tab-glass active" data-toggle="tab" href="#migration-planner">
                            📋 Migration Planner
                        </a>
                        <a class="tab-glass" data-toggle="tab" href="#build-sheets">
                            📊 Build Sheets
                        </a>
                        <a class="tab-glass" data-toggle="tab" href="#migration-tracker">
                            📈 Migration Tracker
                        </a>
                    </div>

                    <div class="tab-content">
                        <!-- Migration Planner Tab -->
                        <div id="migration-planner" class="tab-pane fade in active show">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    Cloud Migration Planning Tool
                                    <span style="color: #3b82f6"> for strategic planning</span>
                                </h4>

                                <!-- Form Section -->
                                <form id="migration-planner-form" action="migration" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'migration-plan','');" style="margin-top: 30px;">
                                    <input type="hidden" name="call" value="planner">

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Migration Project Name</label>
                                        <input type="text" placeholder="Enter project name" name="project_name" class="input-glass" required>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Source Environment</label>
                                        <select name="source_env" class="input-glass" required>
                                            <option value="">Select Source</option>
                                            <option value="on-premises">On-Premises</option>
                                            <option value="aws">Amazon Web Services</option>
                                            <option value="azure">Microsoft Azure</option>
                                            <option value="gcp">Google Cloud Platform</option>
                                            <option value="vmware">VMware vSphere</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Target Cloud Platform</label>
                                        <select name="target_platform" class="input-glass" required>
                                            <option value="">Select Target</option>
                                            <option value="gcp">Google Cloud Platform</option>
                                            <option value="aws">Amazon Web Services</option>
                                            <option value="azure">Microsoft Azure</option>
                                            <option value="multi">Multi-Cloud</option>
                                        </select>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Migration Strategy</label>
                                        <select name="strategy" class="input-glass" required>
                                            <option value="">Select Strategy</option>
                                            <option value="rehost">Rehost (Lift and Shift)</option>
                                            <option value="replatform">Replatform (Lift, Tinker, and Shift)</option>
                                            <option value="refactor">Refactor/Re-architect</option>
                                            <option value="repurchase">Repurchase</option>
                                            <option value="retain">Retain</option>
                                            <option value="retire">Retire</option>
                                        </select>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Timeline (Months)</label>
                                        <select name="timeline" class="input-glass" required>
                                            <option value="">Select Timeline</option>
                                            <option value="3">3 Months</option>
                                            <option value="6">6 Months</option>
                                            <option value="12">12 Months</option>
                                            <option value="18">18 Months</option>
                                            <option value="24">24 Months</option>
                                        </select>
                                    </div>

                                    <button type="submit" class="btn-gradient">Create Migration Plan</button>
                                </form>

                                <div id="text-block-container-migration-plan" style="filter:none; margin-top: 20px;"></div>
                            </div>
                        </div>

                        <!-- Build Sheets Tab -->
                        <div id="build-sheets" class="tab-pane fade">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    Infrastructure Build Sheets
                                    <span style="color: #10b981"> for detailed specifications</span>
                                </h4>

                                <!-- Form Section -->
                                <form id="build-sheets-form" action="migration" method="post" enctype="multipart/form-data"
                                      onsubmit="addUUID(this); revealText(this, 'build-sheets','');" style="margin-top: 30px;">
                                    <input type="hidden" name="call" value="buildsheets">

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Build Sheet Type</label>
                                        <select name="sheet_type" class="input-glass" required>
                                            <option value="">Select Type</option>
                                            <option value="compute">Compute Resources</option>
                                            <option value="storage">Storage Systems</option>
                                            <option value="network">Network Configuration</option>
                                            <option value="database">Database Migration</option>
                                            <option value="application">Application Architecture</option>
                                        </select>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Target Platform</label>
                                        <select name="platform" class="input-glass" required>
                                            <option value="">Select Platform</option>
                                            <option value="gcp">Google Cloud Platform</option>
                                            <option value="aws">Amazon Web Services</option>
                                            <option value="azure">Microsoft Azure</option>
                                        </select>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Environment</label>
                                        <select name="environment" class="input-glass" required>
                                            <option value="">Select Environment</option>
                                            <option value="development">Development</option>
                                            <option value="testing">Testing</option>
                                            <option value="staging">Staging</option>
                                            <option value="production">Production</option>
                                        </select>
                                    </div>

                                    <div class="form-group" style="margin-bottom: 25px;">
                                        <label style="color: #000000; margin-bottom: 8px; font-weight: 500;">Source Configuration File</label>
                                        <input type="file" name="config_file" accept=".csv,.xlsx,.json,.yaml" class="input-glass">
                                    </div>

                                    <button type="submit" class="btn-gradient">Generate Build Sheets</button>
                                </form>

                                <div id="text-block-container-build-sheets" style="filter:none; margin-top: 20px;"></div>
                            </div>
                        </div>

                        <!-- Migration Tracker Tab -->
                        <div id="migration-tracker" class="tab-pane fade">
                            <div class="form-glass">
                                <h4 style="margin-bottom: 20px; color: #000000;">
                                    Migration Progress Tracker
                                    <span style="color: #f59e0b"> for project monitoring</span>
                                </h4>

                                <div style="text-align: center; padding: 40px;">
                                    <p style="color: #666666; margin-bottom: 20px;">Track your migration progress in real-time</p>
                                    <div style="background: rgba(255, 255, 255, 0.05); padding: 30px; border-radius: 12px; margin-bottom: 20px;">
                                        <h5 style="color: #000000; margin-bottom: 15px;">Current Migration Status</h5>
                                        <div style="background: #e5e5e5; height: 20px; border-radius: 10px; overflow: hidden;">
                                            <div style="background: linear-gradient(90deg, #10b981, #059669); height: 100%; width: 65%; border-radius: 10px;"></div>
                                        </div>
                                        <p style="color: #666666; margin-top: 10px;">65% Complete</p>
                                    </div>
                                    <button class="btn-gradient">View Detailed Progress</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Glide Section -->
            <div id="glide-section" class="section-content">
                <div class="content-card">
                    <h3 style="text-align: center; margin-bottom: 30px;">Glide Automation</h3>
                    <p style="text-align: center; margin-bottom: 40px; color: #666666;">
                        Advanced automation tools for cloud operations and management
                    </p>

                    <!-- Glide Features -->
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px;">
                        <div class="form-glass">
                            <h4 style="margin-bottom: 15px; color: #000000;">⚡ Automated Provisioning</h4>
                            <p style="color: #666666; margin-bottom: 20px;">Streamline infrastructure provisioning with intelligent automation workflows.</p>
                            <button class="btn-gradient">Configure Automation</button>
                        </div>

                        <div class="form-glass">
                            <h4 style="margin-bottom: 15px; color: #000000;">🔄 Continuous Optimization</h4>
                            <p style="color: #666666; margin-bottom: 20px;">Automatically optimize cloud resources for cost and performance.</p>
                            <button class="btn-gradient">Enable Optimization</button>
                        </div>

                        <div class="form-glass">
                            <h4 style="margin-bottom: 15px; color: #000000;">📊 Smart Monitoring</h4>
                            <p style="color: #666666; margin-bottom: 20px;">Intelligent monitoring with predictive analytics and alerting.</p>
                            <button class="btn-gradient">Setup Monitoring</button>
                        </div>

                        <div class="form-glass">
                            <h4 style="margin-bottom: 15px; color: #000000;">🛡️ Security Automation</h4>
                            <p style="color: #666666; margin-bottom: 20px;">Automated security scanning and compliance enforcement.</p>
                            <button class="btn-gradient">Configure Security</button>
                        </div>

                        <div class="form-glass">
                            <h4 style="margin-bottom: 15px; color: #000000;">🔧 Self-Healing Systems</h4>
                            <p style="color: #666666; margin-bottom: 20px;">Automated incident response and system recovery capabilities.</p>
                            <button class="btn-gradient">Enable Self-Healing</button>
                        </div>

                        <div class="form-glass">
                            <h4 style="margin-bottom: 15px; color: #000000;">📈 Performance Tuning</h4>
                            <p style="color: #666666; margin-bottom: 20px;">AI-driven performance optimization and resource scaling.</p>
                            <button class="btn-gradient">Start Tuning</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Chatbot -->
    <div class="chatbot-container">
        <div class="chatbot-window" id="chatbot-window">
            <div class="chatbot-header">
                <h4>🤖 HypatIa Assistant</h4>
                <button class="chatbot-close" onclick="toggleChatbot()">×</button>
            </div>
            <div class="chatbot-messages" id="chatbot-messages">
                <div class="message bot">
                    Hello! I'm your HypatIa AI assistant. I can help you with cloud inventory, compliance, assessments, and migration planning. How can I assist you today?
                </div>
            </div>
            <div class="chatbot-input-container">
                <input type="text" class="chatbot-input" id="chatbot-input" placeholder="Type your message..." onkeypress="handleChatKeyPress(event)">
                <button class="chatbot-send" onclick="sendMessage()" id="chatbot-send">Send</button>
            </div>
        </div>
        <div class="chatbot-button" onclick="toggleChatbot()">
            <span>🤖</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="./static/scripts/main.js"></script>
    
    <script>
        // Navigation functionality
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.section-content');
            const pageTitle = document.getElementById('page-title');
            
            const sectionTitles = {
                'inventory': 'Cloud Inventory Management',
                'compliance': 'Compliance Monitoring',
                'assessment': 'Cloud Assessment Tools',
                'migration': 'Migration Planning',
                'glide': 'Glide Automation'
            };
            
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all links and sections
                    navLinks.forEach(l => l.classList.remove('active'));
                    sections.forEach(s => s.classList.remove('active'));
                    
                    // Add active class to clicked link
                    this.classList.add('active');
                    
                    // Show corresponding section
                    const sectionId = this.getAttribute('data-section') + '-section';
                    const targetSection = document.getElementById(sectionId);
                    if (targetSection) {
                        targetSection.classList.add('active');
                    }
                    
                    // Update page title
                    const sectionName = this.getAttribute('data-section');
                    pageTitle.textContent = sectionTitles[sectionName] || 'HypatIa Platform';
                    
                    // Load section content if not already loaded
                    loadSectionContent(sectionName);
                });
            });

            // Load inventory content initially
            loadSectionContent('inventory');
        });
        
        function loadSectionContent(sectionName) {
            // All sections now have their content pre-loaded
            // This function can be used for future dynamic loading if needed
            const section = document.getElementById(sectionName + '-section');

            // Add dashboard-specific content if it's the dashboard section
            if (sectionName === 'dashboard' && section) {
                // Load dashboard stats and widgets
                loadDashboardStats();
            }
        }

        function loadDashboardStats() {
            const dashboardSection = document.getElementById('dashboard-section');
            if (dashboardSection && dashboardSection.children.length === 1) {
                // Add dashboard stats cards
                const statsHTML = `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 24px; margin-bottom: 30px;">
                        <div class="content-card">
                            <h4 style="color: #3b82f6; margin-bottom: 10px;">2,847</h4>
                            <p style="color: #666666; margin: 0;">Total Cloud Resources</p>
                        </div>
                        <div class="content-card">
                            <h4 style="color: #10b981; margin-bottom: 10px;">94%</h4>
                            <p style="color: #666666; margin: 0;">Compliance Score</p>
                        </div>
                        <div class="content-card">
                            <h4 style="color: #06b6d4; margin-bottom: 10px;">156</h4>
                            <p style="color: #666666; margin: 0;">Active Projects</p>
                        </div>
                        <div class="content-card">
                            <h4 style="color: #f59e0b; margin-bottom: 10px;">$12.4K</h4>
                            <p style="color: #666666; margin: 0;">Monthly Savings Potential</p>
                        </div>
                    </div>
                    <div class="content-card">
                        <h4 style="margin-bottom: 20px; color: #000000;">Quick Actions</h4>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                            <button class="btn-glass" onclick="document.querySelector('[data-section=\\"inventory\\"]').click()">📦 Generate Inventory</button>
                            <button class="btn-glass" onclick="document.querySelector('[data-section=\\"compliance\\"]').click()">🛡️ Run Compliance Scan</button>
                            <button class="btn-glass" onclick="document.querySelector('[data-section=\\"assessment\\"]').click()">📋 Create Assessment</button>
                            <button class="btn-glass" onclick="document.querySelector('[data-section=\\"migration\\"]').click()">🚀 Plan Migration</button>
                        </div>
                    </div>
                `;
                dashboardSection.insertAdjacentHTML('beforeend', statsHTML);
            }
        }

        // Info tab functionality
        function openInfoTab(evt, tabName) {
            // Get the parent container to scope the search
            const parentContainer = evt.currentTarget.closest('.form-glass');

            // Check if the clicked tab is already active
            const targetContent = document.getElementById(tabName);
            if (targetContent && targetContent.style.display === "block") {
                targetContent.style.display = "none";
                evt.currentTarget.classList.remove("active");
                return;
            }

            // Hide all info content in the same parent
            const tabContents = parentContainer.getElementsByClassName("info-content");
            for (let i = 0; i < tabContents.length; i++) {
                tabContents[i].style.display = "none";
            }

            // Remove active class from all buttons in the same parent
            const tabButtons = parentContainer.getElementsByClassName("btn-glass");
            for (let i = 0; i < tabButtons.length; i++) {
                tabButtons[i].classList.remove("active");
            }

            // Show the selected content and mark button as active
            if (targetContent) {
                targetContent.style.display = "block";
                targetContent.style.animation = "fadeIn 0.3s ease-out";
                evt.currentTarget.classList.add("active");
            }
        }

        // Bootstrap tab functionality for inventory tabs
        $(document).ready(function() {
            $('.tab-glass[data-toggle="tab"]').click(function(e) {
                e.preventDefault();

                // Remove active class from all tabs
                $('.tab-glass').removeClass('active');
                $('.tab-pane').removeClass('active show');

                // Add active class to clicked tab
                $(this).addClass('active');

                // Show corresponding content with animation
                const target = $(this).attr('href');
                $(target).addClass('active show');
                $(target).css('animation', 'fadeInUp 0.6s ease-out');
            });
        });

        // Original functions from main.js for compatibility
        function addUUID(form) {
            const uuid = crypto.randomUUID().replace(/-/g, '');
            let uuidInput = form.elements["uuid"];
            if (!uuidInput) {
                uuidInput = document.createElement("input");
                uuidInput.type = "hidden";
                uuidInput.name = "uuid";
                uuidInput.id = "uuid-input";
                form.appendChild(uuidInput);
            }
            uuidInput.value = uuid;
        }

        function revealText(form, col, plat) {
            const uuid = crypto.randomUUID().replace(/-/g, '');
            let uuidInput = form.elements["uuid"];
            if (!uuidInput) {
                uuidInput = document.createElement("input");
                uuidInput.type = "hidden";
                uuidInput.name = "uuid";
                uuidInput.id = "uuid-input";
                form.appendChild(uuidInput);
            }
            uuidInput.value = uuid;

            // Remove any existing overlay
            let existingOverlay = document.getElementById("loadingOverlay");
            if (existingOverlay) {
                existingOverlay.remove();
            }

            // Create Loading Overlay with glass effect
            let overlay = document.createElement("div");
            overlay.id = "loadingOverlay";
            overlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.3);
                backdrop-filter: blur(15px);
                -webkit-backdrop-filter: blur(15px);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            `;

            // Create Loading Box
            let loadingBox = document.createElement("div");
            loadingBox.style.cssText = `
                background: rgba(255, 255, 255, 0.15);
                backdrop-filter: blur(25px);
                -webkit-backdrop-filter: blur(25px);
                border: 1px solid rgba(0, 0, 0, 0.2);
                border-radius: 24px;
                padding: 40px;
                text-align: center;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.2);
                max-width: 400px;
                width: 90%;
                position: relative;
            `;

            // Close Button
            let closeButton = document.createElement("button");
            closeButton.innerHTML = "×";
            closeButton.style.cssText = `
                position: absolute;
                top: 15px;
                right: 20px;
                background: none;
                border: none;
                color: #000000;
                font-size: 24px;
                cursor: pointer;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.3s ease;
            `;
            closeButton.onmouseover = function() {
                this.style.background = 'rgba(0, 0, 0, 0.1)';
            };
            closeButton.onmouseout = function() {
                this.style.background = 'none';
            };

            // Title
            let title = document.createElement("h5");
            title.innerText = "Processing Request...";
            title.style.cssText = "color: #000000; margin-bottom: 20px;";

            // Spinner
            let spinner = document.createElement("div");
            spinner.style.cssText = `
                width: 40px;
                height: 40px;
                border: 3px solid rgba(0, 0, 0, 0.2);
                border-top: 3px solid #000000;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 20px;
            `;

            // Timer
            let timer = document.createElement("p");
            timer.id = "timer";
            timer.innerHTML = "<strong>Elapsed Time: 00:00</strong>";
            timer.style.color = "#666666";

            // Append Elements
            loadingBox.appendChild(closeButton);
            loadingBox.appendChild(title);
            loadingBox.appendChild(spinner);
            loadingBox.appendChild(timer);
            overlay.appendChild(loadingBox);
            document.body.appendChild(overlay);

            // Start timer
            let startTime = Date.now();
            let timerInterval = setInterval(function() {
                let elapsed = Math.floor((Date.now() - startTime) / 1000);
                let minutes = Math.floor(elapsed / 60);
                let seconds = elapsed % 60;
                timer.innerHTML = `<strong>Elapsed Time: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}</strong>`;
            }, 1000);

            // Store timer reference for cleanup
            closeButton.onclick = function () {
                clearInterval(timerInterval);
                overlay.remove();
            };
        }

        // Chatbot functionality
        let chatbotOpen = false;
        let messageHistory = [];

        function toggleChatbot() {
            const chatbotWindow = document.getElementById('chatbot-window');
            chatbotOpen = !chatbotOpen;

            if (chatbotOpen) {
                chatbotWindow.classList.add('open');
                document.getElementById('chatbot-input').focus();
            } else {
                chatbotWindow.classList.remove('open');
            }
        }

        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        async function sendMessage() {
            const input = document.getElementById('chatbot-input');
            const message = input.value.trim();

            if (!message) return;

            // Add user message
            addMessage(message, 'user');
            input.value = '';

            // Show typing indicator
            showTypingIndicator();

            // Disable send button
            const sendButton = document.getElementById('chatbot-send');
            sendButton.disabled = true;

            try {
                // Call Vertex AI API
                const response = await callVertexAI(message);

                // Remove typing indicator
                removeTypingIndicator();

                // Add bot response
                addMessage(response, 'bot');
            } catch (error) {
                console.error('Error calling Vertex AI:', error);
                removeTypingIndicator();
                addMessage('I apologize, but I\'m having trouble connecting to my AI service right now. Please try again later.', 'bot');
            } finally {
                // Re-enable send button
                sendButton.disabled = false;
            }
        }

        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chatbot-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.textContent = text;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;

            // Store in history
            messageHistory.push({ text, sender, timestamp: new Date() });
        }

        function showTypingIndicator() {
            const messagesContainer = document.getElementById('chatbot-messages');
            const typingDiv = document.createElement('div');
            typingDiv.className = 'typing-indicator';
            typingDiv.id = 'typing-indicator';
            typingDiv.innerHTML = '<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>';

            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function removeTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        async function callVertexAI(message) {
            // This is a placeholder for Vertex AI integration
            // In a real implementation, you would call the Vertex AI API

            // For demo purposes, return contextual responses based on the current section
            const currentSection = document.querySelector('.nav-link.active').getAttribute('data-section');

            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

            // Generate contextual responses
            const responses = {
                inventory: [
                    "I can help you generate cloud inventory reports. You can create inventories for GCP, AWS, Azure, Oracle Cloud, GKE, and EKS. Which platform would you like to start with?",
                    "For GCP inventory, you'll need Organization ID, Folder IDs, or Project IDs. Make sure you have the required permissions set up.",
                    "AWS inventory requires an ARN with ReadOnlyAccess policy. I can guide you through the CloudFormation setup if needed.",
                    "Azure inventory needs a Service Principal with Reader access. Would you like help setting up the credentials?"
                ],
                compliance: [
                    "I can assist with compliance monitoring across GCP, AWS, and Azure. Which compliance framework are you interested in - CIS, NIST, ISO 27001, or SOC 2?",
                    "For GCP compliance, you'll need Security Reviewer access and enabled APIs. I can help you understand the requirements.",
                    "AWS compliance scanning supports multiple frameworks including AWS Foundational Security Standard and PCI DSS.",
                    "Azure compliance assessment covers Azure Security Benchmark and various industry standards."
                ],
                assessment: [
                    "I can help with cloud assessments including migration readiness, VMware analysis, and cost assessments. What type of assessment are you planning?",
                    "For migration assessment, I'll need information about your current infrastructure and target cloud platform.",
                    "VMware assessment requires vCenter access and helps plan your cloud migration strategy.",
                    "Cost assessment can provide TCO analysis and ROI calculations for your cloud migration."
                ],
                migration: [
                    "I can assist with migration planning using the 6 R's strategy: Rehost, Replatform, Refactor, Repurchase, Retain, or Retire. Which approach fits your needs?",
                    "Migration planning involves assessing your current environment and creating detailed build sheets for the target platform.",
                    "I can help you create migration timelines and track progress throughout your cloud journey.",
                    "Build sheets provide detailed specifications for recreating your infrastructure in the cloud."
                ],
                glide: [
                    "Glide automation provides advanced cloud operations including automated provisioning, optimization, and monitoring.",
                    "I can help you set up automated provisioning workflows to streamline your infrastructure deployment.",
                    "Continuous optimization features help reduce costs and improve performance automatically.",
                    "Smart monitoring with predictive analytics can help prevent issues before they occur."
                ]
            };

            // Get random response for current section or general response
            const sectionResponses = responses[currentSection] || [
                "I'm here to help with all aspects of cloud management including inventory, compliance, assessments, migration, and automation.",
                "You can ask me about any of the HypatIa features - I have detailed knowledge about cloud inventory generation, compliance monitoring, migration planning, and more.",
                "Feel free to ask specific questions about cloud platforms, requirements, or step-by-step guidance for any process."
            ];

            // Simple keyword matching for more relevant responses
            const lowerMessage = message.toLowerCase();
            if (lowerMessage.includes('gcp') || lowerMessage.includes('google')) {
                return "For Google Cloud Platform, I can help with inventory generation, compliance scanning, and migration planning. You'll typically need Organization ID and appropriate IAM permissions.";
            } else if (lowerMessage.includes('aws') || lowerMessage.includes('amazon')) {
                return "For AWS, I can assist with inventory reports, compliance assessments, and EKS analysis. You'll need an ARN with ReadOnlyAccess permissions.";
            } else if (lowerMessage.includes('azure') || lowerMessage.includes('microsoft')) {
                return "For Microsoft Azure, I can help with inventory generation and compliance monitoring. You'll need a Service Principal with Reader access.";
            } else if (lowerMessage.includes('help') || lowerMessage.includes('how')) {
                return "I'm here to guide you through any HypatIa process. You can ask about specific requirements, step-by-step instructions, or best practices for cloud management.";
            }

            return sectionResponses[Math.floor(Math.random() * sectionResponses.length)];
        }
    </script>
</body>
</html>
